# 黑长条距离标定系统使用说明

## 修改概述

已将原来基于最大矩形的距离标定系统改为基于黑长条的标定系统。

## 主要修改内容

### 1. 配置参数修改
- `BLACK_STRIP_PIXELS = 0.0` - 标定时黑长条的像素长度（需要用户填入）
- `BLACK_STRIP_PHYSICAL_LENGTH = 26.0` - 黑长条的物理长度（厘米）
- `CALIBRATION_DISTANCE = 160.0` - 标定距离（厘米）
- 新增黑长条检测参数：
  - `BLACK_STRIP_MIN_AREA = 500` - 最小面积
  - `BLACK_STRIP_ASPECT_RATIO = 3.0` - 最小长宽比
  - `BLACK_STRIP_MAX_ASPECT_RATIO = 15.0` - 最大长宽比

### 2. 新增功能类
- `BlackStripDetector` - 黑长条检测器
- 修改 `DistanceMeasurement` - 改为基于黑长条的距离测量

### 3. 距离计算公式
```
距离 = (标定像素长度 × 标定距离) / 当前黑长条像素长度
距离 = (BLACK_STRIP_PIXELS × 160.0) / current_strip_length
```

## 使用步骤

### 第一步：准备黑长条
- 制作一个长度为 **26cm** 的黑色长条
- 长条应该是纯黑色，便于检测
- 长宽比建议在 3:1 到 15:1 之间

### 第二步：标定
1. 将黑长条放置在距离摄像头 **160cm** 的位置
2. 运行程序
3. 程序会自动检测黑长条并显示其像素长度
4. 在控制台会看到类似信息：
   ```
   🎯 检测到标定黑长条，长度: 228.50 像素
   📏 请将 BLACK_STRIP_PIXELS 设置为: 228.50
   ```

### 第三步：设置参数
1. 停止程序
2. 在代码第50行修改 `BLACK_STRIP_PIXELS` 的值：
   ```python
   BLACK_STRIP_PIXELS = 228.5  # 填入程序显示的像素长度
   ```
3. 保存并重新运行程序

### 第四步：测试
1. 重新运行程序
2. 将黑长条放在不同距离处
3. 程序会显示实时距离测量结果

## 检测原理

### 黑长条检测条件
1. **面积过滤**：轮廓面积 > 500 像素
2. **形状过滤**：长宽比在 3:1 到 15:1 之间
3. **自动选择**：选择检测到的最长的黑长条作为标定对象

### 距离计算原理
基于相似三角形原理：
- 物体距离越远，在图像中显示越小
- 通过已知距离下的像素尺寸作为参考
- 使用反比例关系计算当前距离

## 注意事项

1. **黑长条要求**：
   - 颜色要足够黑，与背景对比明显
   - 形状要规整，避免弯曲或变形
   - 长度要准确（26cm）

2. **环境要求**：
   - 光照要均匀，避免强烈阴影
   - 背景要相对简单，避免干扰
   - 黑长条要完全在摄像头视野内

3. **标定精度**：
   - 标定距离要准确测量（160cm）
   - 黑长条要垂直于摄像头光轴放置
   - 多次测量取平均值可提高精度

## 显示信息

程序运行时会显示：
- 标定黑长条的轮廓（黄色）
- 距离测量结果（橙色文字）
- 检测状态信息

## 故障排除

1. **检测不到黑长条**：
   - 检查黑长条颜色是否足够黑
   - 调整 `BLACK_STRIP_MIN_AREA` 参数
   - 检查长宽比是否在范围内

2. **距离测量不准确**：
   - 重新精确测量标定距离
   - 确认黑长条长度准确
   - 重新标定像素长度

3. **检测到多个长条**：
   - 程序会自动选择最长的一个
   - 确保环境中只有一个目标黑长条
