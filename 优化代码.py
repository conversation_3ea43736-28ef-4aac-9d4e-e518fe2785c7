from maix import image, camera, display, app, time
import cv2
import numpy as np

# ==================== 优化后的参数配置 ====================
class Config:
    """统一的配置管理类"""
    # 图像处理参数（参考重叠识别文档）
    BINARY_THRESHOLD = 60  # 参考文档中的阈值
    MIN_AREA = 100
    EPSILON_FACTOR = 0.01  # 参考文档中的1%精度
    
    # 标定参数
    CALIBRATION_DISTANCE = 130.0
    CALIBRATION_PIXELS = 176.0
    SECOND_LARGEST_RECT_PHYSICAL_LENGTH = 19.1
    
    # 检测参数
    DUPLICATE_DISTANCE_THRESHOLD = 15
    DUPLICATE_AREA_RATIO = 0.8
    POSITION_TOLERANCE = 20
    
    # 多边形分析参数
    ENABLE_POLYGON_CORNER_ANALYSIS = True
    POLYGON_EDGE_OVERLAP_THRESHOLD = 0.5
    ENABLE_ISOLATED_CORNER_DETECTION = True
    
    # 显示参数
    SHOW_DEBUG = True
    DEBUG_VIEW = 2  # 0: 原图, 1: 二值化, 2: 边缘, 3: 闭运算后

# ==================== 优化后的工具函数 ====================
class GeometryUtils:
    """几何计算工具类"""
    
    @staticmethod
    def calculate_distance(point1, point2):
        """计算两点距离"""
        return np.sqrt((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)
    
    @staticmethod
    def calculate_area_ratio(area1, area2):
        """计算面积比例"""
        max_area = max(area1, area2)
        return min(area1, area2) / max_area if max_area > 0 else 1.0
    
    @staticmethod
    def get_contour_center(contour):
        """获取轮廓中心点"""
        M = cv2.moments(contour)
        if M["m00"] == 0:
            return None
        return int(M["m10"] / M["m00"]), int(M["m01"] / M["m00"])

# ==================== 优化后的图像处理类 ====================
class ImageProcessor:
    """统一的图像处理类"""
    
    def __init__(self, config):
        self.config = config
        self.roi_rect = None
        self.roi_valid = False
        
    def process_image(self, img_cv, use_roi=False):
        """优化的图像处理流程（参考重叠识别文档）"""
        # 1. 转换为灰度图
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)

        # 2. ROI处理
        if use_roi and self.roi_valid:
            x, y, w, h = self.roi_rect
            gray = gray[y:y+h, x:x+w]
            roi_offset = (x, y)
        else:
            roi_offset = (0, 0)

        # 3. 二值化处理（参考重叠识别文档的方法）
        _, binary = cv2.threshold(gray, self.config.BINARY_THRESHOLD, 255, cv2.THRESH_BINARY)

        # 4. 图像翻转（用于检测高亮区域，黑变白、白变黑）
        flipped_binary = cv2.bitwise_not(binary)

        # 返回翻转后的二值图像和原始二值图像
        return flipped_binary, binary, roi_offset
    
    def find_contours(self, flipped_binary):
        """查找并排序轮廓（参考重叠识别文档方法）"""
        # 使用外轮廓检测和简化方式存储轮廓点
        contours, _ = cv2.findContours(flipped_binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        return sorted(contours, key=cv2.contourArea, reverse=True)

# ==================== 优化后的形状检测类 ====================
class ShapeDetector:
    """统一的形状检测类"""
    
    def __init__(self, config, geometry_utils):
        self.config = config
        self.geometry = geometry_utils
        self.detected_shapes = []
        
    def detect_shape(self, contour, roi_offset=(0, 0)):
        """统一的形状检测函数"""
        # 计算面积
        area = cv2.contourArea(contour)
        if area < self.config.MIN_AREA:
            return None
            
        # 获取中心点
        center = self.geometry.get_contour_center(contour)
        if center is None:
            return None
            
        # 调整坐标（如果使用ROI）
        cx, cy = center[0] + roi_offset[0], center[1] + roi_offset[1]
        
        # 轮廓近似
        epsilon = self.config.EPSILON_FACTOR * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)
        num_vertices = len(approx)
        
        # 形状分类
        shape_info = {
            'contour': contour,
            'approx': approx,
            'center': (cx, cy),
            'area': area,
            'vertices': num_vertices,
            'type': self._classify_shape(num_vertices)
        }
        
        # 检查重复
        if not self._is_duplicate(shape_info):
            self.detected_shapes.append(shape_info)
            return shape_info
            
        return None
    
    def _classify_shape(self, num_vertices):
        """形状分类"""
        if num_vertices == 3:
            return 'triangle'
        elif num_vertices == 4:
            return 'quadrilateral'
        elif num_vertices >= 5:
            return 'polygon'
        else:
            return 'unknown'
    
    def _is_duplicate(self, shape_info):
        """检查是否重复"""
        cx, cy = shape_info['center']
        area = shape_info['area']
        vertices = shape_info['vertices']
        
        for detected in self.detected_shapes:
            if detected['vertices'] == vertices:
                distance = self.geometry.calculate_distance((cx, cy), detected['center'])
                area_ratio = self.geometry.calculate_area_ratio(area, detected['area'])
                
                if (distance < self.config.DUPLICATE_DISTANCE_THRESHOLD and 
                    area_ratio > self.config.DUPLICATE_AREA_RATIO):
                    return True
        return False
    
    def reset(self):
        """重置检测结果"""
        self.detected_shapes = []

# ==================== 优化后的多边形分析类 ====================
class PolygonAnalyzer:
    """多边形拐点分析类"""

    def __init__(self, config):
        self.config = config

    def analyze_polygon(self, shape_info, flipped_binary, reference_pixels, physical_length):
        """分析多边形拐点（使用新的二值化图像）"""
        if shape_info['type'] != 'polygon':
            return None

        if not self.config.ENABLE_POLYGON_CORNER_ANALYSIS:
            return None

        # 提取噪声点（使用翻转后的二值图像）
        noise_points = self._extract_noise_points(shape_info['contour'], flipped_binary)

        # 处理拐点分析
        result = self._process_polygon_corners(
            shape_info['approx'],
            reference_pixels,
            physical_length,
            noise_points
        )

        return result

    def _extract_noise_points(self, contour, flipped_binary):
        """提取轮廓内部噪声点（使用翻转后的二值图像）"""
        if flipped_binary is None or len(contour) < 3:
            return []

        x, y, w, h = cv2.boundingRect(contour)
        noise_points = []

        # 在翻转后的二值图像中，白色像素(255)表示原来的黑色区域
        # 我们寻找轮廓内部的白色像素作为噪声点
        for py in range(y, y + h):
            for px in range(x, x + w):
                if cv2.pointPolygonTest(contour, (px, py), False) > 0:
                    if flipped_binary[py, px] > 0:  # 白色像素
                        noise_points.append((px, py))

        return noise_points

    def _process_polygon_corners(self, approx, reference_pixels, physical_length, noise_points):
        """处理多边形拐点（集成原有逻辑）"""
        vertices = [(point[0][0], point[0][1]) for point in approx]

        # 拐点标记（简化版，实际可以集成原有的复杂逻辑）
        corner_marks = [1] * len(vertices)  # 默认都是有效拐点

        # 计算边长
        edge_lengths = []
        valid_edges = []

        for i in range(len(vertices)):
            next_i = (i + 1) % len(vertices)
            edge_length = GeometryUtils.calculate_distance(vertices[i], vertices[next_i])
            edge_lengths.append(edge_length)

            # 只有连接两个有效拐点的边才是有效边
            if corner_marks[i] == 1 and corner_marks[next_i] == 1:
                valid_edges.append(edge_length)

        # 计算最小有效边长
        min_edge_pixels = min(valid_edges) if valid_edges else 0

        # 转换为物理长度
        if reference_pixels > 0 and min_edge_pixels > 0:
            min_edge_physical = min_edge_pixels * physical_length / reference_pixels
        else:
            min_edge_physical = 0

        # 孤立拐点处理（简化版）
        isolated_corners = [i for i, mark in enumerate(corner_marks) if mark == 2]

        if isolated_corners and self.config.ENABLE_ISOLATED_CORNER_DETECTION:
            self._handle_isolated_corners(vertices, isolated_corners, noise_points)

        return {
            'vertices': vertices,
            'corner_marks': corner_marks,
            'edge_lengths': edge_lengths,
            'min_edge_physical': min_edge_physical,
            'noise_points_count': len(noise_points),
            'isolated_corners': isolated_corners
        }

    def _handle_isolated_corners(self, vertices, isolated_corners, noise_points):
        """处理孤立拐点（简化版）"""
        if len(isolated_corners) == 1:
            # 单个孤立拐点：使用噪声点进行处理
            isolated_point = vertices[isolated_corners[0]]
            closest_noise = self._find_closest_noise_point(isolated_point, noise_points)
            if closest_noise:
                print(f"找到孤立拐点的最近噪声点: {closest_noise}")

        elif len(isolated_corners) == 2:
            # 两个孤立拐点：连接处理
            print(f"处理两个孤立拐点: {isolated_corners}")

        elif len(isolated_corners) == 3:
            # 三个孤立拐点：连接首尾处理
            print(f"处理三个孤立拐点: {isolated_corners}")

    def _find_closest_noise_point(self, isolated_point, noise_points):
        """从噪声点中找到最近的点"""
        if not noise_points:
            return None

        min_distance = float('inf')
        closest_point = None

        for noise_point in noise_points:
            distance = GeometryUtils.calculate_distance(isolated_point, noise_point)
            if distance < min_distance:
                min_distance = distance
                closest_point = noise_point

        return closest_point

# ==================== 预处理优化类 ====================
class PreprocessOptimizer:
    """预处理和ROI优化类"""

    def __init__(self, config):
        self.config = config
        self.max_rectangles = []
        self.stable_frames = 0
        self.stable_threshold = 2
        self.roi_rect = None
        self.roi_valid = False

    def update_max_rectangles(self, detected_shapes):
        """更新最大矩形信息"""
        # 找出所有四边形
        quadrilaterals = [shape for shape in detected_shapes if shape['type'] == 'quadrilateral']

        if len(quadrilaterals) >= 2:
            # 按面积排序，取最大的两个
            quadrilaterals.sort(key=lambda x: x['area'], reverse=True)
            current_max = quadrilaterals[:2]

            # 检查是否稳定
            if self._is_rectangles_stable(current_max):
                self.stable_frames += 1
                if self.stable_frames >= self.stable_threshold:
                    self.max_rectangles = current_max
                    self._update_roi()
            else:
                self.stable_frames = 0
        else:
            self.stable_frames = 0

    def _is_rectangles_stable(self, current_rectangles):
        """检查矩形是否稳定"""
        if len(self.max_rectangles) != 2 or len(current_rectangles) != 2:
            return False

        # 简化的稳定性检查：比较中心点距离
        for i in range(2):
            old_center = self.max_rectangles[i]['center']
            new_center = current_rectangles[i]['center']
            distance = GeometryUtils.calculate_distance(old_center, new_center)
            if distance > 10:  # 阈值
                return False
        return True

    def _update_roi(self):
        """更新ROI区域"""
        if len(self.max_rectangles) == 2:
            # 计算包含两个最大矩形的ROI区域
            all_points = []
            for rect in self.max_rectangles:
                for point in rect['approx']:
                    all_points.append([point[0][0], point[0][1]])

            if all_points:
                all_points = np.array(all_points)
                x_min, y_min = np.min(all_points, axis=0)
                x_max, y_max = np.max(all_points, axis=0)

                # 添加边距
                margin = 20
                self.roi_rect = (
                    max(0, x_min - margin),
                    max(0, y_min - margin),
                    x_max - x_min + 2 * margin,
                    y_max - y_min + 2 * margin
                )
                self.roi_valid = True

    def get_roi_info(self):
        """获取ROI信息"""
        return self.roi_rect, self.roi_valid

# ==================== 主应用类 ====================
class VisionApp:
    """主应用类"""

    def __init__(self):
        self.config = Config()
        self.geometry = GeometryUtils()
        self.image_processor = ImageProcessor(self.config)
        self.shape_detector = ShapeDetector(self.config, self.geometry)
        self.polygon_analyzer = PolygonAnalyzer(self.config)
        self.preprocessor = PreprocessOptimizer(self.config)

        # 初始化相机和显示
        self.cam = camera.Camera(1024, 768, image.Format.FMT_BGR888)
        self.disp = display.Display()

        # 统计变量
        self.frame_count = 0
        
    def run(self):
        """优化后的主运行循环"""
        while not app.need_exit():
            self.frame_count += 1

            # 1. 图像采集
            img_maix = self.cam.read()
            img_cv = image.image2cv(img_maix, ensure_bgr=True, copy=False)

            # 2. 更新ROI信息
            roi_rect, roi_valid = self.preprocessor.get_roi_info()
            self.image_processor.roi_rect = roi_rect
            self.image_processor.roi_valid = roi_valid

            # 3. 图像处理（自动使用ROI优化）
            use_roi = roi_valid and self.frame_count > 5  # 前几帧不使用ROI
            flipped_binary, binary, roi_offset = self.image_processor.process_image(img_cv, use_roi)

            # 4. 轮廓检测
            contours = self.image_processor.find_contours(flipped_binary)

            # 5. 统一形状检测
            detected_shapes = self._detect_all_shapes(contours, roi_offset)

            # 6. 预处理优化：更新最大矩形
            self.preprocessor.update_max_rectangles(detected_shapes)

            # 7. 多边形分析
            self._analyze_polygons(detected_shapes, flipped_binary)

            # 8. 结果显示
            img_result = self._draw_results(img_cv, detected_shapes)

            # 9. 显示图像
            self.disp.show(img_result)

    def _detect_all_shapes(self, contours, roi_offset):
        """统一的形状检测流程"""
        self.shape_detector.reset()
        detected_shapes = []

        for contour in contours:
            shape_info = self.shape_detector.detect_shape(contour, roi_offset)
            if shape_info:
                detected_shapes.append(shape_info)

        return detected_shapes

    def _analyze_polygons(self, detected_shapes, flipped_binary):
        """分析所有多边形"""
        reference_pixels = self._get_reference_pixels()

        for shape_info in detected_shapes:
            if shape_info['type'] == 'polygon':
                analysis_result = self.polygon_analyzer.analyze_polygon(
                    shape_info, flipped_binary, reference_pixels,
                    self.config.SECOND_LARGEST_RECT_PHYSICAL_LENGTH
                )
                if analysis_result:
                    shape_info['analysis'] = analysis_result
    
    def _get_reference_pixels(self):
        """获取参考像素值"""
        # 从最大矩形中获取参考像素值
        if len(self.preprocessor.max_rectangles) >= 2:
            # 获取第二大矩形的最长边
            second_rect = self.preprocessor.max_rectangles[1]
            vertices = [(point[0][0], point[0][1]) for point in second_rect['approx']]

            # 计算所有边长
            edge_lengths = []
            for i in range(len(vertices)):
                next_i = (i + 1) % len(vertices)
                edge_length = self.geometry.calculate_distance(vertices[i], vertices[next_i])
                edge_lengths.append(edge_length)

            return max(edge_lengths)  # 返回最长边

        # 回退到配置值
        return self.config.CALIBRATION_PIXELS

    def _draw_results(self, img_cv, detected_shapes):
        """优化后的结果绘制"""
        # 根据调试模式选择显示内容
        if self.config.SHOW_DEBUG:
            if self.config.DEBUG_VIEW == 0:
                img_result = image.cv2image(img_cv, bgr=True, copy=False)
            elif self.config.DEBUG_VIEW == 1:
                # 显示二值化图像
                gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
                _, binary = cv2.threshold(gray, self.config.BINARY_THRESHOLD, 255, cv2.THRESH_BINARY)
                binary_colored = np.stack([binary, binary, binary], axis=2)
                img_result = image.cv2image(binary_colored)
            elif self.config.DEBUG_VIEW == 2:
                # 显示翻转后的二值图像
                flipped_binary, _, _ = self.image_processor.process_image(img_cv)
                flipped_colored = np.stack([flipped_binary, flipped_binary, flipped_binary], axis=2)
                img_result = image.cv2image(flipped_colored)
            else:
                img_result = image.cv2image(img_cv, bgr=True, copy=False)
        else:
            img_result = image.cv2image(img_cv, bgr=True, copy=False)

        # 绘制检测到的形状
        self._draw_shapes(img_result, img_cv, detected_shapes)

        # 绘制统计信息
        self._draw_statistics(img_result, detected_shapes)

        return img_result

    def _draw_shapes(self, img_result, img_cv, detected_shapes):
        """绘制形状"""
        for shape_info in detected_shapes:
            center = shape_info['center']
            shape_type = shape_info['type']

            # 选择颜色
            color_map = {
                'triangle': image.COLOR_RED,
                'quadrilateral': image.COLOR_GREEN,
                'polygon': image.COLOR_PURPLE
            }
            color = color_map.get(shape_type, image.COLOR_WHITE)

            # 绘制轮廓
            cv2.drawContours(img_cv, [shape_info['contour']], -1, (0, 255, 0), 2)

            # 绘制中心点和标签
            img_result.draw_circle(center[0], center[1], 3, color, -1)
            img_result.draw_string(center[0] + 10, center[1], f"{shape_type}", color)

            # 绘制多边形分析结果
            if 'analysis' in shape_info:
                self._draw_polygon_analysis(img_result, shape_info, color)

    def _draw_polygon_analysis(self, img_result, shape_info, color):
        """绘制多边形分析结果"""
        analysis = shape_info['analysis']
        center = shape_info['center']

        # 显示最小边长
        min_edge = analysis['min_edge_physical']
        img_result.draw_string(center[0] + 10, center[1] + 20,
                             f"Min: {min_edge:.1f}cm", color)

        # 显示噪声点数量
        noise_count = analysis['noise_points_count']
        img_result.draw_string(center[0] + 10, center[1] + 40,
                             f"Noise: {noise_count}", color)

        # 显示孤立拐点信息
        if analysis['isolated_corners']:
            isolated_count = len(analysis['isolated_corners'])
            img_result.draw_string(center[0] + 10, center[1] + 60,
                                 f"Isolated: {isolated_count}", color)

    def _draw_statistics(self, img_result, detected_shapes):
        """绘制统计信息"""
        # 统计各种形状数量
        stats = {'triangle': 0, 'quadrilateral': 0, 'polygon': 0}
        for shape in detected_shapes:
            shape_type = shape['type']
            if shape_type in stats:
                stats[shape_type] += 1

        # 显示统计信息
        y_offset = 30
        img_result.draw_string(10, y_offset, f"Frame: {self.frame_count}", image.COLOR_WHITE)
        y_offset += 20

        for shape_type, count in stats.items():
            if count > 0:
                img_result.draw_string(10, y_offset, f"{shape_type}: {count}", image.COLOR_WHITE)
                y_offset += 20

        # 显示ROI状态
        if self.preprocessor.roi_valid:
            img_result.draw_string(10, y_offset, "ROI: Active", image.COLOR_GREEN)
        else:
            img_result.draw_string(10, y_offset, "ROI: Inactive", image.COLOR_RED)

# ==================== 程序入口 ====================
if __name__ == "__main__":
    vision_app = VisionApp()
    vision_app.run()
