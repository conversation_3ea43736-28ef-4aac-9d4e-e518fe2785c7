from maix import image, camera, display, app, time
import cv2
import numpy as np

# ==================== 参数区 ====================

class Config:
    """配置参数类"""
    # 硬件参数
    CAMERA_WIDTH = 1024
    CAMERA_HEIGHT = 720
    
    # 图像处理参数
    BINARY_THRESHOLD = 100
    GAUSSIAN_KERNEL_SIZE = 3  # 高斯滤波核大小（必须为奇数）
    GAUSSIAN_SIGMA = 1.0      # 高斯滤波标准差
    CANNY_LOW = 50
    CANNY_HIGH = 120
    MIN_AREA = 100
    EPSILON_FACTOR = 0.1
    
    # 形状检测参数
    CIRCULARITY_THRESHOLD = 0.85 # 圆形检测阈值
    DUPLICATE_DISTANCE_THRESHOLD = 15 # 重复形状距离阈值（像素）
    DUPLICATE_AREA_RATIO = 0.8 # 重复形状面积比例阈值
    AREA_OVERLAP_THRESHOLD = 0.7 # 面积重叠阈值
    POLYGON_OVERLAP_THRESHOLD = 0.7 # 多边形重叠阈值

    # 跟踪参数
    POSITION_TOLERANCE = 50 # 位置容忍度（像素）
    VERTEX_HISTORY_SIZE = 3 # 顶点历史记录长度
    EDGE_HISTORY_SIZE = 3 # 边缘历史记录长度
    SHAPE_TIMEOUT = 10 # 形状超时时间（帧数）

    # 预处理参数
    ENABLE_PREPROCESS = True                 # 是否启用预处理
    PREPROCESS_START_FRAME = 2            # 预处理开始帧数
    PREPROCESS_STABLE_THRESHOLD = 1  # 稳定帧数阈值
    
    # ROI优化参数
    USE_ROI_OPTIMIZATION = True
    ROI_EXPAND_PIXELS = 3 # ROI扩展像素数（用于扩大ROI区域）
    
    # 四边形区域检测参数
    QUAD_MASK_EXPAND_PIXELS = 10  # 四边形掩码扩展像素数
    
    # 距离测量参数（请根据实际情况修改）
    ENABLE_DISTANCE_MEASUREMENT = True
    CALIBRATION_DISTANCE = 160.0  # 标定时的实际距离（厘米）
    BLACK_STRIP_PIXELS = 0.0      # 标定时黑长条的像素长度（请在这里输入实际值）
    # 使用方法：运行程序后，将26cm长的黑长条放在160cm处，程序会检测并显示像素长度
    # 然后将显示的像素长度填入上面的 BLACK_STRIP_PIXELS 参数中
    BLACK_STRIP_PHYSICAL_LENGTH = 26.0   # 黑长条的物理长度（厘米）
    DISTANCE_HISTORY_SIZE = 5

    # 黑长条检测参数
    BLACK_STRIP_MIN_AREA = 500    # 黑长条最小面积
    BLACK_STRIP_ASPECT_RATIO = 3.0  # 黑长条最小长宽比（长/宽）
    BLACK_STRIP_MAX_ASPECT_RATIO = 15.0  # 黑长条最大长宽比
    
    # 双四边形预处理参数
    DUAL_RECT_CENTER_TOLERANCE = 30  # 两个四边形中心位置容忍度（像素）
    DUAL_RECT_SIZE_RATIO_THRESHOLD = 0.65  # 小四边形相对于大四边形的面积比例阈值
    
    # 框内统计参数
    ENABLE_INNER_SHAPE_STATS = True
    
    # 显示参数
    SHOW_DEBUG = True
    DEBUG_VIEW = 2  # 0:原图 1:二值 2:边缘
    SHOW_EDGE_LENGTHS = True
    SHOW_SHAPE_AREAS = False
    SHOW_INNER_SHAPE_STATS = True
    USE_INSTANT_VALUES = True  # 使用当前测量值而非平均值
    SHOW_FILTER_DEBUG = True  # 是否显示过滤调试信息

class GlobalState:
    """全局状态管理"""
    def __init__(self):
        self.frame_count = 0
        self.view_switch_count = 0
        self.current_distance = 0.0
        self.distance_history = []
        self.calibration_strip = None  # 存储标定黑长条信息
        self.detected_strips = []      # 存储检测到的所有黑长条
        
        # 预处理状态
        self.max_rectangles = []  # 存储选择的标定矩形（基于双四边形分析或单一最大矩形）
        self.preprocess_started = False
        self.preprocess_stable_frames = 0
        
        # 缓存状态
        self.cached_mask = None
        self.cached_mask_valid = False
        self.last_rectangles_centers = []
        self.cached_roi_rect = None
        self.roi_valid = False
        
        # 跟踪数据
        self.shape_tracking_data = {}
        self.vertex_history = {}
        self.edge_history = {}
        self.circle_radius_history = {}
        self.last_frame_shapes = {}
        
        # 统计数据
        self.inner_shapes_stats = {
            'triangles': {'count': 0, 'avg_edge_length': 0.0, 'total_edge_length': 0.0,
                         'avg_edge_length_physical': 0.0, 'total_edge_length_physical': 0.0},
            'quadrilaterals': {'count': 0, 'avg_edge_length': 0.0, 'total_edge_length': 0.0,
                              'avg_edge_length_physical': 0.0, 'total_edge_length_physical': 0.0},
            'circles': {'count': 0, 'avg_radius': 0.0, 'total_radius': 0.0,
                       'avg_radius_physical': 0.0, 'total_radius_physical': 0.0},
            'polygons': {'count': 0, 'avg_edge_length': 0.0, 'total_edge_length': 0.0,
                        'avg_edge_length_physical': 0.0, 'total_edge_length_physical': 0.0}
        }
        
        # 形状计数历史记录
        self.triangle_counts = []
        self.quadrilateral_counts = []
        self.circle_counts = []
        self.polygon_counts = []
        
        # 矩形尺寸历史记录
        self.max_rect1_sizes = []
        self.max_rect2_sizes = []

# 全局实例
config = Config()
state = GlobalState()


class UtilityFunctions:
    """实用函数类"""
    @staticmethod
    def get_value_from_history(values, use_latest=True):
        """从历史记录中获取值，默认使用最新值"""
        if not values:
            return 0
        return values[-1] if use_latest or len(values) < 2 else int(sum(values[-3:]) / min(len(values), 3))
    
    @staticmethod
    def is_point_inside_polygon(point, polygon_points):
        """判断点是否在多边形内部"""
        try:
            if polygon_points is None or len(polygon_points) < 3:
                return False
            
            # 转换格式
            if isinstance(polygon_points, np.ndarray):
                if len(polygon_points.shape) == 3:
                    polygon_contour = polygon_points.astype(np.int32)
                elif len(polygon_points.shape) == 2:
                    polygon_contour = polygon_points.reshape(-1, 1, 2).astype(np.int32)
                else:
                    return False
            else:
                try:
                    polygon_contour = np.array(polygon_points, dtype=np.int32).reshape(-1, 1, 2)
                except Exception:
                    return False
            
            px, py = float(point[0]), float(point[1])
            result = cv2.pointPolygonTest(polygon_contour, (px, py), False)
            return result >= 0
            
        except Exception as e:
            print(f"判断点是否在多边形内出错: {e}")
            return False
    
    @staticmethod
    def collect_fine_polygons_from_quads(quadrilaterals, img_cv, edges, min_area):
        """从所有四边形区域收集精细检测的多边形"""
        collected_polygons = []
        
        # 只在预处理完成后才进行多边形检测
        if not (config.ENABLE_PREPROCESS and state.preprocess_started and 
                len(state.max_rectangles) == 1 and 
                state.preprocess_stable_frames >= config.PREPROCESS_STABLE_THRESHOLD):
            return collected_polygons
        
        fine_epsilon_factor = 0.01
        
        try:
            for quad_info in quadrilaterals:
                quad_approx = quad_info['approx']
                
                # 创建四边形区域的掩码
                if edges.shape != img_cv.shape[:2]:
                    # ROI模式：调整坐标
                    if state.roi_valid and state.cached_roi_rect:
                        roi_x, roi_y, roi_w, roi_h = state.cached_roi_rect
                        adjusted_quad = quad_approx.copy()
                        adjusted_quad[:, 0, 0] -= roi_x
                        adjusted_quad[:, 0, 1] -= roi_y
                        
                        mask = np.zeros(edges.shape, dtype=np.uint8)
                        cv2.fillPoly(mask, [adjusted_quad], 255)
                    else:
                        continue
                else:
                    # 非ROI模式
                    mask = np.zeros(edges.shape, dtype=np.uint8)
                    cv2.fillPoly(mask, [quad_approx], 255)
                
                # 在四边形区域内查找轮廓
                masked_edges = cv2.bitwise_and(edges, mask)
                contours, _ = cv2.findContours(masked_edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                for contour in contours:
                    area = cv2.contourArea(contour)
                    if area < min_area:
                        continue
                    
                    # 使用精细的epsilon_factor
                    epsilon = fine_epsilon_factor * cv2.arcLength(contour, True)
                    approx = cv2.approxPolyDP(contour, epsilon, True)
                    num_vertices = len(approx)
                    
                    # 只收集多边形（5个或更多顶点）
                    if num_vertices >= 5:
                        M = cv2.moments(contour)
                        if M["m00"] != 0:
                            cx = int(M["m10"] / M["m00"])
                            cy = int(M["m01"] / M["m00"])
                            
                            # 如果是ROI模式，需要转换回原图坐标
                            if edges.shape != img_cv.shape[:2] and state.roi_valid and state.cached_roi_rect:
                                roi_x, roi_y, roi_w, roi_h = state.cached_roi_rect
                                cx += roi_x
                                cy += roi_y
                                
                                # 调整approx坐标
                                adjusted_approx = approx.copy()
                                adjusted_approx[:, 0, 0] += roi_x
                                adjusted_approx[:, 0, 1] += roi_y
                                approx = adjusted_approx
                            
                            # 确保坐标格式正确
                            formatted_approx = np.array([[int(pt[0][0]), int(pt[0][1])] for pt in approx], dtype=np.int32)
                            formatted_approx = formatted_approx.reshape(-1, 1, 2)
                            
                            collected_polygons.append((cx, cy, formatted_approx))
        
        except Exception as e:
            print(f"收集精细多边形时出错: {e}")
        
        return collected_polygons
    
    @staticmethod
    def calculate_distance_between_shapes(shape1, shape2):
        """计算两个形状之间的距离（以像素为单位）"""
        try:
            # 获取形状的中心点
            if isinstance(shape1, tuple) and len(shape1) >= 2:
                center1 = (shape1[0], shape1[1])
            else:
                return float('inf')
            
            if isinstance(shape2, tuple) and len(shape2) >= 2:
                center2 = (shape2[0], shape2[1])
            else:
                return float('inf')
            
            # 计算欧几里得距离
            distance = ((center1[0] - center2[0]) ** 2 + (center1[1] - center2[1]) ** 2) ** 0.5
            return distance
        except:
            return float('inf')
    
    @staticmethod
    def check_shapes_overlap(shape1_contour, shape2_contour):
        """检查两个形状是否重叠"""
        try:
            # 计算轮廓的边界框
            x1, y1, w1, h1 = cv2.boundingRect(shape1_contour)
            x2, y2, w2, h2 = cv2.boundingRect(shape2_contour)
            
            # 检查边界框是否重叠
            if (x1 < x2 + w2 and x1 + w1 > x2 and
                y1 < y2 + h2 and y1 + h1 > y2):
                # 如果边界框重叠，进一步检查轮廓重叠
                # 创建掩码
                mask_size = (max(x1 + w1, x2 + w2) + 10, max(y1 + h1, y2 + h2) + 10)
                mask1 = np.zeros(mask_size, dtype=np.uint8)
                mask2 = np.zeros(mask_size, dtype=np.uint8)
                
                # 绘制轮廓到掩码
                cv2.fillPoly(mask1, [shape1_contour], 255)
                cv2.fillPoly(mask2, [shape2_contour], 255)
                
                # 检查交集
                intersection = cv2.bitwise_and(mask1, mask2)
                return cv2.countNonZero(intersection) > 0
            
            return False
        except:
            return False


class BlackStripDetector:
    """黑长条检测器"""

    @staticmethod
    def detect_black_strips(contours):
        """检测黑长条"""
        black_strips = []

        for contour in contours:
            area = cv2.contourArea(contour)
            if area < config.BLACK_STRIP_MIN_AREA:
                continue

            # 计算边界矩形
            rect = cv2.minAreaRect(contour)
            width, height = rect[1]

            # 确保width是较小的边，height是较大的边
            if width > height:
                width, height = height, width

            # 计算长宽比
            if width > 0:
                aspect_ratio = height / width
                if (config.BLACK_STRIP_ASPECT_RATIO <= aspect_ratio <= config.BLACK_STRIP_MAX_ASPECT_RATIO):
                    # 计算中心点
                    M = cv2.moments(contour)
                    if M["m00"] != 0:
                        cx = int(M["m10"] / M["m00"])
                        cy = int(M["m01"] / M["m00"])

                        black_strips.append({
                            'contour': contour,
                            'center': (cx, cy),
                            'length': height,  # 长边作为长度
                            'width': width,    # 短边作为宽度
                            'area': area,
                            'aspect_ratio': aspect_ratio
                        })

        # 按长度排序，返回最长的黑长条
        black_strips.sort(key=lambda x: x['length'], reverse=True)
        return black_strips

    @staticmethod
    def get_calibration_strip_length():
        """获取标定黑长条的长度"""
        if hasattr(state, 'calibration_strip') and state.calibration_strip:
            return state.calibration_strip['length']
        return 0.0

class DistanceCalculator:
    """距离计算类"""

    @staticmethod
    def calculate_real_size(pixel_distance):
        """根据像素距离计算真实尺寸（毫米）"""
        try:
            # 获取参考黑长条的像素长度
            if config.BLACK_STRIP_PIXELS <= 0:
                return "未知"

            # 使用黑长条的物理长度作为参考
            real_size_mm = (pixel_distance * config.BLACK_STRIP_PHYSICAL_LENGTH) / config.BLACK_STRIP_PIXELS

            return f"{real_size_mm:.1f}mm"
        except:
            return "未知"
    
    @staticmethod
    def get_black_strip_length():
        """获取标定黑长条的长度"""
        if state.calibration_strip:
            return state.calibration_strip['length']
        return 0.0
    
    @staticmethod
    def calculate_distance_to_calibration_strip(shape_center):
        """计算形状到标定黑长条的距离"""
        distances = {}

        if state.calibration_strip:
            # 计算标定黑长条的中心
            strip_center = state.calibration_strip['center']
            dist = ((shape_center[0] - strip_center[0])**2 + (shape_center[1] - strip_center[1])**2)**0.5
            distances['calibration_strip'] = {
                'pixel': int(dist),
                'real': DistanceCalculator.calculate_real_size(dist)
            }

        return distances


class StatisticsManager:
    """统计管理类"""
    
    @staticmethod
    def update_shape_statistics(triangles, quadrilaterals, circles, polygons):
        """更新形状统计"""
        state.triangle_counts.append(len(triangles))
        state.quadrilateral_counts.append(len(quadrilaterals))
        state.circle_counts.append(len(circles))
        state.polygon_counts.append(len(polygons))
        
        # 保持历史记录长度
        max_history = 30
        if len(state.triangle_counts) > max_history:
            state.triangle_counts.pop(0)
        if len(state.quadrilateral_counts) > max_history:
            state.quadrilateral_counts.pop(0)
        if len(state.circle_counts) > max_history:
            state.circle_counts.pop(0)
        if len(state.polygon_counts) > max_history:
            state.polygon_counts.pop(0)
    
    @staticmethod
    def get_average_counts():
        """获取平均计数"""
        return {
            'triangles': UtilityFunctions.get_value_from_history(state.triangle_counts, False),
            'quadrilaterals': UtilityFunctions.get_value_from_history(state.quadrilateral_counts, False),
            'circles': UtilityFunctions.get_value_from_history(state.circle_counts, False),
            'polygons': UtilityFunctions.get_value_from_history(state.polygon_counts, False)
        }
    
    @staticmethod
    def print_detailed_statistics():
        """打印详细统计信息"""
        if state.frame_count % 30 == 0:  # 每30帧打印一次
            avg_counts = StatisticsManager.get_average_counts()
            print(f"\n=== 第{state.frame_count}帧统计 ===")
            print(f"平均形状数量: 三角形={avg_counts['triangles']}, 四边形={avg_counts['quadrilaterals']}, 圆形={avg_counts['circles']}, 多边形={avg_counts['polygons']}")
            
            if len(state.max_rectangles) >= 1:
                rect1_size = UtilityFunctions.get_value_from_history(state.max_rect1_sizes, False)
                print(f"参考矩形大小: 矩形={rect1_size}px")
            
            if state.roi_valid:
                print(f"ROI状态: 激活 {state.cached_roi_rect}")
            else:
                print("ROI状态: 未激活")


# ==================== 功能函数区 ====================

class MathUtils:
    """数学计算工具类"""
    @staticmethod
    def calculate_distance(point1, point2):
        """计算两点间距离"""
        return np.sqrt((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)
    
    @staticmethod
    def calculate_area_ratio(area1, area2):
        """计算面积比例"""
        max_area = max(area1, area2)
        return min(area1, area2) / max_area if max_area > 0 else 1.0
    
    @staticmethod
    def calculate_longest_edge(vertices):
        """计算最长边"""
        if len(vertices) < 2:
            return 0
        max_length = 0
        for i in range(len(vertices)):
            pt1, pt2 = vertices[i], vertices[(i+1) % len(vertices)]
            edge_length = MathUtils.calculate_distance(pt1, pt2)
            max_length = max(max_length, edge_length)
        return max_length
    
    @staticmethod
    def calculate_average_edge_length(vertices):
        """计算平均边长"""
        if len(vertices) < 2:
            return 0.0
        total_length = 0
        for i in range(len(vertices)):
            pt1, pt2 = vertices[i], vertices[(i+1) % len(vertices)]
            total_length += MathUtils.calculate_distance(pt1, pt2)
        return total_length / len(vertices)
    
    @staticmethod
    def calculate_physical_size(pixel_size, reference_pixels, reference_physical_size):
        """根据参考尺寸计算物理尺寸"""
        return (pixel_size * reference_physical_size) / reference_pixels if reference_pixels > 0 else 0.0

class ImageProcessor:
    """图像处理工具类"""
    @staticmethod
    def preprocess_image(img_cv, roi_rect=None):
        """图像预处理"""
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        
        # ROI优化
        if roi_rect and config.USE_ROI_OPTIMIZATION and state.roi_valid:
            x, y, w, h = roi_rect
            gray_roi = gray[y:y+h, x:x+w]
            roi_offset = (x, y)
        else:
            gray_roi = gray
            roi_offset = (0, 0)
        
        # 二值化、高斯滤波和边缘检测
        _, binary = cv2.threshold(gray_roi, config.BINARY_THRESHOLD, 255, cv2.THRESH_BINARY)
        
        # 在二值化之后添加高斯滤波以减少噪声
        binary_filtered = cv2.GaussianBlur(binary, (config.GAUSSIAN_KERNEL_SIZE, config.GAUSSIAN_KERNEL_SIZE), config.GAUSSIAN_SIGMA)
        
        # 使用滤波后的二值图像进行边缘检测
        edges = cv2.Canny(binary_filtered, config.CANNY_LOW, config.CANNY_HIGH)
        
        return gray, binary_filtered, edges, roi_offset
    
    @staticmethod
    def find_contours_with_roi_adjustment(edges, roi_offset):
        """查找轮廓并调整ROI坐标"""
        contours, _ = cv2.findContours(edges, cv2.RETR_TREE, cv2.CHAIN_APPROX_NONE)
        
        # 调整ROI坐标
        if roi_offset != (0, 0):
            roi_x, roi_y = roi_offset
            adjusted_contours = []
            for contour in contours:
                adjusted_contour = contour.copy()
                adjusted_contour[:, 0, 0] += roi_x
                adjusted_contour[:, 0, 1] += roi_y
                adjusted_contours.append(adjusted_contour)
            return adjusted_contours
        
        return contours

class ShapeDetector:
    """形状检测器"""
    @staticmethod
    def classify_shape(contour, epsilon_factor=None):
        """分类形状"""
        if epsilon_factor is None:
            epsilon_factor = config.EPSILON_FACTOR
            
        area = cv2.contourArea(contour)
        if area < config.MIN_AREA:
            return None
        
        # 计算中心点
        M = cv2.moments(contour)
        if M["m00"] == 0:
            return None
        cx, cy = int(M["m10"] / M["m00"]), int(M["m01"] / M["m00"])
        
        # 轮廓近似
        epsilon = epsilon_factor * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)
        num_vertices = len(approx)
        
        # 圆形检测
        perimeter = cv2.arcLength(contour, True)
        circularity = 4 * np.pi * area / (perimeter * perimeter) if perimeter > 0 else 0
        
        shape_info = {
            'type': None,
            'approx': approx,
            'cx': cx,
            'cy': cy,
            'area': area,
            'vertices_count': num_vertices
        }
        
        if circularity > config.CIRCULARITY_THRESHOLD:
            shape_info['type'] = 'Circle'
            shape_info['radius'] = int(np.sqrt(area / np.pi))
            shape_info['vertices'] = None
        elif num_vertices == 3:
            shape_info['type'] = 'Triangle'
            shape_info['vertices'] = [tuple(pt[0]) for pt in approx]
        elif num_vertices == 4:
            shape_info['type'] = 'Quad'
            shape_info['vertices'] = [tuple(pt[0]) for pt in approx]
        elif num_vertices >= 5:
            shape_info['type'] = 'Polygon'
            shape_info['vertices'] = [tuple(pt[0]) for pt in approx]
        
        return shape_info
    
    @staticmethod
    def is_duplicate_shape(shape_info, detected_shapes):
        """检查重复形状"""
        cx, cy, area = shape_info['cx'], shape_info['cy'], shape_info['area']
        vertices_count = shape_info['vertices_count']
        
        for detected_cx, detected_cy, detected_area, detected_vertices in detected_shapes:
            if detected_vertices == vertices_count:
                distance = MathUtils.calculate_distance((cx, cy), (detected_cx, detected_cy))
                area_ratio = MathUtils.calculate_area_ratio(area, detected_area)
                if (distance < config.DUPLICATE_DISTANCE_THRESHOLD and 
                    area_ratio > config.DUPLICATE_AREA_RATIO):
                    return True
        return False

class OverlapCalculator:
    """重叠计算器"""
    @staticmethod
    def calculate_overlap_area(shape1_approx, shape2_approx, canvas_size=800):
        """计算两个形状的重叠面积比例"""
        try:
            # 计算第一个形状面积
            area1 = cv2.contourArea(shape1_approx)
            if area1 <= 0:
                return 0.0
            
            # 处理坐标
            points1 = shape1_approx.reshape(-1, 2)
            points2 = shape2_approx.reshape(-1, 2)
            all_points = np.vstack([points1, points2])
            
            min_x, min_y = np.min(all_points, axis=0)
            max_x, max_y = np.max(all_points, axis=0)
            
            # 动态画布大小
            shape_width, shape_height = max_x - min_x, max_y - min_y
            canvas_size = max(canvas_size, int(max(shape_width, shape_height) * 2 + 100))
            
            # 计算偏移
            offset_x = canvas_size // 2 - (min_x + max_x) // 2
            offset_y = canvas_size // 2 - (min_y + max_y) // 2
            
            # 调整坐标并绘制掩码
            mask1 = np.zeros((canvas_size, canvas_size), dtype=np.uint8)
            mask2 = np.zeros((canvas_size, canvas_size), dtype=np.uint8)
            
            adjusted_points1 = points1 + [offset_x, offset_y]
            adjusted_points2 = points2 + [offset_x, offset_y]
            
            cv2.fillPoly(mask1, [adjusted_points1.astype(np.int32)], 255)
            cv2.fillPoly(mask2, [adjusted_points2.astype(np.int32)], 255)
            
            # 计算重叠
            overlap_mask = cv2.bitwise_and(mask1, mask2)
            overlap_area = cv2.countNonZero(overlap_mask)
            shape1_pixels = cv2.countNonZero(mask1)
            
            return overlap_area / shape1_pixels if shape1_pixels > 0 else 0.0
            
        except Exception as e:
            print(f"计算重叠面积出错: {e}")
            return 0.0
    
    @staticmethod
    def calculate_circle_quad_overlap(quad_approx, circle_cx, circle_cy, circle_radius):
        """计算四边形与圆形重叠（保持向后兼容）"""
        return OverlapCalculator.calculate_circle_polygon_overlap(
            quad_approx, circle_cx, circle_cy, circle_radius
        )
    
    @staticmethod
    def calculate_circle_polygon_overlap(polygon_approx, circle_cx, circle_cy, circle_radius):
        """计算任意多边形与圆形的重叠比例（基于多边形面积）"""
        try:
            polygon_area = cv2.contourArea(polygon_approx)
            if polygon_area <= 0:
                return 0.0
            
            # 创建足够大的画布
            canvas_size = max(800, int(circle_radius * 3))
            offset_x = canvas_size // 2 - circle_cx
            offset_y = canvas_size // 2 - circle_cy
            
            # 调整多边形坐标
            adjusted_polygon = polygon_approx.copy()
            adjusted_polygon[:, 0, 0] += offset_x
            adjusted_polygon[:, 0, 1] += offset_y
            
            # 绘制掩码
            polygon_mask = np.zeros((canvas_size, canvas_size), dtype=np.uint8)
            circle_mask = np.zeros((canvas_size, canvas_size), dtype=np.uint8)
            
            # 绘制多边形
            polygon_points = adjusted_polygon.reshape(-1, 2)
            cv2.fillPoly(polygon_mask, [polygon_points], 255)
            
            # 绘制圆形
            cv2.circle(circle_mask, (canvas_size // 2, canvas_size // 2), circle_radius, 255, -1)
            
            # 计算重叠
            overlap_mask = cv2.bitwise_and(polygon_mask, circle_mask)
            overlap_area = cv2.countNonZero(overlap_mask)
            polygon_pixels = cv2.countNonZero(polygon_mask)
            
            return overlap_area / polygon_pixels if polygon_pixels > 0 else 0.0
            
        except Exception as e:
            print(f"计算圆形多边形重叠出错: {e}")
            return 0.0

class ShapeFilter:
    """形状过滤器 - 基于优先级的泛用过滤体系"""
    
    # 形状优先级定义（数字越高优先级越高）
    SHAPE_PRIORITY = {
        'Polygon': 4,   # 最高优先级
        'Circle': 3,    # 第二优先级
        'Quad': 2,      # 第三优先级
        'Triangle': 1   # 最低优先级
    }
    
    @staticmethod
    def apply_priority_based_filtering(shape_candidates):
        """基于优先级的形状过滤系统"""
        if config.SHOW_FILTER_DEBUG:
            print("🔍 开始优先级过滤...")
        
        # 创建所有形状的列表并按优先级排序
        all_shapes = []
        for shape_type, shapes in shape_candidates.items():
            for shape_info in shapes:
                shape_info['priority'] = ShapeFilter.SHAPE_PRIORITY.get(shape_type, 0)
                all_shapes.append(shape_info)
        
        # 按优先级从高到低排序
        all_shapes.sort(key=lambda x: x['priority'], reverse=True)
        
        # 保留的形状列表
        filtered_shapes = {'Triangle': [], 'Quad': [], 'Circle': [], 'Polygon': []}
        
        # 从高优先级到低优先级依次处理
        for current_shape in all_shapes:
            should_keep = True
            current_type = current_shape['type']
            
            # 检查是否被更高优先级的形状过滤
            for kept_shape_type, kept_shapes in filtered_shapes.items():
                kept_priority = ShapeFilter.SHAPE_PRIORITY.get(kept_shape_type, 0)
                current_priority = current_shape['priority']
                
                # 只检查优先级更高或相等的已保留形状
                if kept_priority >= current_priority:
                    for kept_shape in kept_shapes:
                        if ShapeFilter._check_overlap_and_filter(current_shape, kept_shape):
                            if config.SHOW_FILTER_DEBUG:
                                print(f"  ❌ {current_type} 被 {kept_shape_type} 过滤 (重叠度过高)")
                            should_keep = False
                            break
                
                if not should_keep:
                    break
            
            # 如果通过过滤，保留该形状
            if should_keep:
                filtered_shapes[current_type].append(current_shape)
                if config.SHOW_FILTER_DEBUG:
                    print(f"  ✅ 保留 {current_type} (优先级: {current_shape['priority']})")
        
        return filtered_shapes
    
    @staticmethod
    def _check_overlap_and_filter(shape1, shape2):
        """检查两个形状是否重叠并决定是否过滤"""
        try:
            # 获取形状类型
            type1, type2 = shape1['type'], shape2['type']
            
            # 计算重叠度
            overlap_ratio = 0.0
            
            # 圆形与其他形状的重叠计算
            if type1 == 'Circle' and type2 in ['Triangle', 'Quad', 'Polygon']:
                overlap_ratio = OverlapCalculator.calculate_circle_polygon_overlap(
                    shape2['approx'], shape1['cx'], shape1['cy'], shape1['radius']
                )
            elif type2 == 'Circle' and type1 in ['Triangle', 'Quad', 'Polygon']:
                overlap_ratio = OverlapCalculator.calculate_circle_polygon_overlap(
                    shape1['approx'], shape2['cx'], shape2['cy'], shape2['radius']
                )
            
            # 多边形形状之间的重叠计算（包括三角形、四边形、多边形）
            elif type1 in ['Triangle', 'Quad', 'Polygon'] and type2 in ['Triangle', 'Quad', 'Polygon']:
                overlap_ratio = OverlapCalculator.calculate_overlap_area(
                    shape1['approx'], shape2['approx']
                )
            
            # 两个圆形之间的重叠（如果需要）
            elif type1 == 'Circle' and type2 == 'Circle':
                # 圆形之间的重叠可以通过距离和半径计算
                distance = MathUtils.calculate_distance(
                    (shape1['cx'], shape1['cy']), 
                    (shape2['cx'], shape2['cy'])
                )
                r1, r2 = shape1['radius'], shape2['radius']
                if distance < r1 + r2:  # 如果圆形相交
                    # 简化处理：如果中心距离小于较大半径，认为有显著重叠
                    overlap_ratio = max(0, 1.0 - distance / max(r1, r2))
                else:
                    overlap_ratio = 0.0
            
            # 判断是否需要过滤
            threshold = config.AREA_OVERLAP_THRESHOLD
            if overlap_ratio >= threshold:
                if config.SHOW_FILTER_DEBUG:
                    print(f"    📊 {type1} vs {type2} 重叠度: {overlap_ratio:.3f} >= {threshold} (过滤)")
                return True
            else:
                if config.SHOW_FILTER_DEBUG:
                    print(f"    📊 {type1} vs {type2} 重叠度: {overlap_ratio:.3f} < {threshold} (保留)")
                return False
                
        except Exception as e:
            print(f"重叠检查出错: {e}")
            return False
    
    @staticmethod
    def should_filter_shape(shape_info, detected_circles, detected_polygons):
        """旧版本兼容方法 - 已被优先级过滤替代"""
        # 保留此方法以确保向后兼容，但实际不再使用
        return False

class ShapeTracker:
    """形状跟踪器"""
    @staticmethod
    def find_matching_position(cx, cy, shape_type):
        """查找匹配的形状位置"""
        min_distance = float('inf')
        shape_position = None
        
        for pos in state.shape_tracking_data.keys():
            if pos[0] == shape_type:
                distance = MathUtils.calculate_distance((cx, cy), (pos[1], pos[2]))
                if distance < config.POSITION_TOLERANCE and distance < min_distance:
                    min_distance = distance
                    shape_position = pos
        
        return shape_position
    
    @staticmethod
    def update_tracking(shape_info):
        """更新形状跟踪"""
        cx, cy = shape_info['cx'], shape_info['cy']
        shape_type = shape_info['type']
        vertices = shape_info.get('vertices')
        
        shape_position = ShapeTracker.find_matching_position(cx, cy, shape_type)
        
        if shape_position is None:
            shape_position = (shape_type, cx, cy)
            state.shape_tracking_data[shape_position] = state.frame_count
            if vertices:
                state.vertex_history[shape_position] = [vertices]
        else:
            state.shape_tracking_data[shape_position] = state.frame_count
            if vertices:
                if shape_position in state.vertex_history:
                    state.vertex_history[shape_position].append(vertices)
                    if len(state.vertex_history[shape_position]) > config.VERTEX_HISTORY_SIZE:
                        state.vertex_history[shape_position].pop(0)
                else:
                    state.vertex_history[shape_position] = [vertices]
        
        # 记录到当前帧
        if shape_type not in state.last_frame_shapes:
            state.last_frame_shapes[shape_type] = []
        state.last_frame_shapes[shape_type].append((cx, cy, shape_info['area']))
        
        return shape_position
    
    @staticmethod
    def cleanup_expired_shapes():
        """清理过期形状"""
        expired_positions = []
        for pos, last_seen_frame in state.shape_tracking_data.items():
            if state.frame_count - last_seen_frame > config.SHAPE_TIMEOUT:
                expired_positions.append(pos)
        
        cleaned_count = 0
        for pos in expired_positions:
            state.shape_tracking_data.pop(pos, None)
            state.vertex_history.pop(pos, None)
            state.circle_radius_history.pop(pos, None)
            
            # 清理边长历史
            edge_keys_to_remove = [key for key in state.edge_history.keys() 
                                 if isinstance(key, tuple) and len(key) >= 1 and key[0] == pos]
            for key in edge_keys_to_remove:
                state.edge_history.pop(key, None)
            
            cleaned_count += 1
        
        return cleaned_count

class DistanceMeasurement:
    """距离测量器"""
    @staticmethod
    def is_calibration_strip(strip_info):
        """判断是否为标定的黑长条"""
        if not state.calibration_strip:
            return False

        # 比较位置和尺寸
        cal_center = state.calibration_strip['center']
        strip_center = strip_info['center']

        distance = MathUtils.calculate_distance(cal_center, strip_center)
        length_ratio = MathUtils.calculate_area_ratio(
            strip_info['length'], state.calibration_strip['length']
        )

        return (distance < config.DUPLICATE_DISTANCE_THRESHOLD and
                length_ratio > config.DUPLICATE_AREA_RATIO)

    @staticmethod
    def calculate_distance_from_strip(strip_info):
        """根据黑长条计算距离"""
        if not config.ENABLE_DISTANCE_MEASUREMENT:
            return None

        if config.BLACK_STRIP_PIXELS <= 0:
            print("⚠️ 警告：BLACK_STRIP_PIXELS未设置，请在配置中输入标定时的像素长度")
            return None

        current_length = strip_info['length']

        if current_length > 0:
            # 使用反比例关系计算距离
            calculated_distance = (config.BLACK_STRIP_PIXELS * config.CALIBRATION_DISTANCE) / current_length

            state.distance_history.append(calculated_distance)
            if len(state.distance_history) > config.DISTANCE_HISTORY_SIZE:
                state.distance_history.pop(0)

            state.current_distance = sum(state.distance_history) / len(state.distance_history)

            return state.current_distance

        return None

    @staticmethod
    def update_calibration_strip(strips):
        """更新标定黑长条"""
        if not strips:
            return False

        # 选择最长的黑长条作为标定长条
        longest_strip = strips[0]  # strips已经按长度排序

        if not state.calibration_strip:
            state.calibration_strip = longest_strip
            print(f"🎯 检测到标定黑长条，长度: {longest_strip['length']:.2f} 像素")
            print(f"📏 请将 BLACK_STRIP_PIXELS 设置为: {longest_strip['length']:.2f}")
            return True

        return False

class StatsUpdater:
    """统计更新器"""
    @staticmethod
    def update_shape_stats(shape_info):
        """更新形状统计"""
        if not config.ENABLE_INNER_SHAPE_STATS:
            return
        
        shape_type = shape_info['type']
        reference_pixels = StatsUpdater._get_reference_pixels()
        
        if shape_type == 'Circle':
            StatsUpdater._update_circle_stats(shape_info, reference_pixels)
        elif shape_type in ['Triangle', 'Quad', 'Polygon']:
            StatsUpdater._update_polygon_stats(shape_info, reference_pixels)
    
    @staticmethod
    def _get_reference_pixels():
        """获取参考像素长度"""
        if state.calibration_strip:
            return state.calibration_strip['length']
        return 0.0
    
    @staticmethod
    def _update_circle_stats(shape_info, reference_pixels):
        """更新圆形统计"""
        radius = shape_info['radius']
        radius_physical = MathUtils.calculate_physical_size(
            radius, reference_pixels, config.BLACK_STRIP_PHYSICAL_LENGTH
        ) if reference_pixels > 0 else 0.0
        
        stats = state.inner_shapes_stats['circles']
        stats['count'] += 1
        stats['total_radius'] += radius
        stats['total_radius_physical'] += radius_physical
        stats['avg_radius'] = stats['total_radius'] / stats['count']
        stats['avg_radius_physical'] = stats['total_radius_physical'] / stats['count']
    
    @staticmethod
    def _update_polygon_stats(shape_info, reference_pixels):
        """更新多边形统计"""
        shape_type = shape_info['type']
        vertices = shape_info['vertices']
        
        if not vertices:
            return
        
        avg_edge_pixels = MathUtils.calculate_average_edge_length(vertices)
        avg_edge_physical = MathUtils.calculate_physical_size(
            avg_edge_pixels, reference_pixels, config.BLACK_STRIP_PHYSICAL_LENGTH
        ) if reference_pixels > 0 else 0.0
        
        stats_key = {'Triangle': 'triangles', 'Quad': 'quadrilaterals', 'Polygon': 'polygons'}[shape_type]
        stats = state.inner_shapes_stats[stats_key]
        
        stats['count'] += 1
        stats['total_edge_length'] += avg_edge_pixels
        stats['total_edge_length_physical'] += avg_edge_physical
        stats['avg_edge_length'] = stats['total_edge_length'] / stats['count']
        stats['avg_edge_length_physical'] = stats['total_edge_length_physical'] / stats['count']
    
    @staticmethod
    def reset_stats():
        """重置统计数据"""
        for shape_type in state.inner_shapes_stats:
            stats = state.inner_shapes_stats[shape_type]
            for key in stats:
                stats[key] = 0.0 if 'avg' in key or 'total' in key else 0

class Renderer:
    """渲染器"""
    @staticmethod
    def draw_shape(img_result, shape_info, shape_position, is_max_rect=False, is_second_largest=False):
        """绘制形状"""
        cx, cy = shape_info['cx'], shape_info['cy']
        shape_type = shape_info['type']
        
        # 设置颜色，特殊处理四边形
        if shape_type == 'Quad':
            if is_max_rect:
                color = image.COLOR_YELLOW
            else:
                color = image.COLOR_GREEN
        else:
            color_map = {
                'Triangle': image.COLOR_RED,
                'Circle': image.COLOR_BLUE,
                'Polygon': image.COLOR_PURPLE
            }
            color = color_map.get(shape_type, image.COLOR_WHITE)
        
        # 绘制标签
        label = shape_type
        if shape_type == 'Polygon':
            label = f"Polygon({shape_info['vertices_count']})"
        elif shape_type == 'Quad' and is_max_rect:
            label = "Quad(标定)"
        
        img_result.draw_string(cx-20, cy, label, color)
        
        # 绘制面积（如果启用）
        if config.SHOW_SHAPE_AREAS:
            area_text = f"A:{int(shape_info['area'])}"
            img_result.draw_string(cx-20, cy+15, area_text, color)
        
        # 绘制形状特有元素
        if shape_type == 'Circle':
            Renderer._draw_circle(img_result, shape_info, shape_position, color)
        else:
            Renderer._draw_polygon_edges(img_result, shape_info, shape_position, color)
    
    @staticmethod
    def _draw_circle(img_result, shape_info, shape_position, color):
        """绘制圆形"""
        cx, cy, radius = shape_info['cx'], shape_info['cy'], shape_info['radius']
        
        # 处理半径历史
        if shape_position not in state.circle_radius_history:
            state.circle_radius_history[shape_position] = []
        
        state.circle_radius_history[shape_position].append(radius)
        if len(state.circle_radius_history[shape_position]) > config.EDGE_HISTORY_SIZE:
            state.circle_radius_history[shape_position].pop(0)
        
        display_radius = UtilityFunctions.get_value_from_history(
            state.circle_radius_history[shape_position], config.USE_INSTANT_VALUES
        )
        
        img_result.draw_circle(cx, cy, display_radius, color, thickness=2)
        img_result.draw_circle(cx, cy, 3, color, thickness=-1)
        
        if config.SHOW_EDGE_LENGTHS:
            radius_text = f"R:{display_radius}"
            img_result.draw_string(cx + 5, cy, radius_text, image.COLOR_RED)
    
    @staticmethod
    def _draw_polygon_edges(img_result, shape_info, shape_position, color):
        """绘制多边形边缘"""
        vertices = shape_info['vertices']
        if not vertices:
            return
        
        for i in range(len(vertices)):
            pt1, pt2 = vertices[i], vertices[(i+1) % len(vertices)]
            
            # 绘制边
            img_result.draw_line(pt1[0], pt1[1], pt2[0], pt2[1], color, 2)
            img_result.draw_circle(pt1[0], pt1[1], 3, color, thickness=-1)
            
            # 计算并显示边长
            if config.SHOW_EDGE_LENGTHS:
                edge_length = int(MathUtils.calculate_distance(pt1, pt2))
                edge_key = (shape_position, i)
                
                if edge_key not in state.edge_history:
                    state.edge_history[edge_key] = []
                
                state.edge_history[edge_key].append(edge_length)
                if len(state.edge_history[edge_key]) > config.EDGE_HISTORY_SIZE:
                    state.edge_history[edge_key].pop(0)
                
                display_length = UtilityFunctions.get_value_from_history(
                    state.edge_history[edge_key], config.USE_INSTANT_VALUES
                )
                mid_x, mid_y = (pt1[0] + pt2[0]) // 2, (pt1[1] + pt2[1]) // 2
                img_result.draw_string(mid_x, mid_y, str(display_length), image.COLOR_RED)
    
    @staticmethod
    def draw_info_overlay(img_result):
        """绘制信息覆盖层"""
        y_offset = 8
        
        # ROI信息
        if config.USE_ROI_OPTIMIZATION and state.roi_valid:
            x, y, w, h = state.cached_roi_rect
            total_area = config.CAMERA_WIDTH * config.CAMERA_HEIGHT
            roi_percent = (w*h)/total_area*100 if total_area > 0 else 0
            roi_info = f"ROI: {w}x{h} ({roi_percent:.0f}%)"
            img_result.draw_string(8, y_offset, roi_info, image.COLOR_GREEN)
            y_offset += 22
        
        # 距离信息
        if config.ENABLE_DISTANCE_MEASUREMENT and state.current_distance > 0:
            distance_info = f"Distance: {state.current_distance:.1f}cm"
            img_result.draw_string(8, y_offset, distance_info, image.COLOR_ORANGE)
            y_offset += 22
        
        # 统计信息
        if config.ENABLE_INNER_SHAPE_STATS and config.SHOW_INNER_SHAPE_STATS:
            y_offset = Renderer._draw_stats_info(img_result, y_offset)
        
        return y_offset
    
    @staticmethod
    def _draw_stats_info(img_result, y_offset):
        """绘制统计信息"""
        stats_map = [
            ('triangles', 'Triangles', image.COLOR_RED),
            ('quadrilaterals', 'Quads', image.COLOR_GREEN),
            ('circles', 'Circles', image.COLOR_BLUE),
            ('polygons', 'Polygons', image.COLOR_PURPLE)
        ]
        
        for stats_key, label, color in stats_map:
            stats = state.inner_shapes_stats[stats_key]
            if stats['count'] > 0:
                if stats_key == 'circles':
                    info = f"{label}: {stats['count']}, Avg R: {stats['avg_radius_physical']:.2f}cm"
                else:
                    info = f"{label}: {stats['count']}, Avg: {stats['avg_edge_length_physical']:.2f}cm"
                img_result.draw_string(8, y_offset, info, color)
                y_offset += 22
        
        return y_offset
    
    @staticmethod
    def highlight_max_rectangles(img_result):
        """高亮显示最大矩形"""
        if not state.max_rectangles or len(state.max_rectangles) < 1:
            return
        
        # 在图像上绘制最大矩形的轮廓
        _, _, approx = state.max_rectangles[0]
        # 转换为点列表
        points = [tuple(pt[0]) for pt in approx]
        # 使用红色标记矩形
        highlight_color = image.COLOR_RED
        
        # 绘制粗线条轮廓
        for j in range(len(points)):
            pt1 = points[j]
            pt2 = points[(j+1) % len(points)]
            img_result.draw_line(pt1[0], pt1[1], pt2[0], pt2[1], highlight_color, 3)
        
        # 在矩形上标注
        M = cv2.moments(approx)
        if M["m00"] != 0:
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])
            img_result.draw_string(cx-30, cy, f"标定矩形", highlight_color)
        
        # 绘制ROI边界（如果启用ROI优化）
        if config.USE_ROI_OPTIMIZATION and state.roi_valid and state.cached_roi_rect:
            x, y, w, h = state.cached_roi_rect
            # 绘制ROI边界矩形
            img_result.draw_line(x, y, x+w, y, image.COLOR_GREEN, 2)  # 上边
            img_result.draw_line(x+w, y, x+w, y+h, image.COLOR_GREEN, 2)  # 右边
            img_result.draw_line(x+w, y+h, x, y+h, image.COLOR_GREEN, 2)  # 下边
            img_result.draw_line(x, y+h, x, y, image.COLOR_GREEN, 2)  # 左边
            # 在ROI左上角标注
            img_result.draw_string(x+5, y+5, "ROI", image.COLOR_GREEN)

# ==================== 流程函数区 ====================

class PreprocessingManager:
    """预处理管理器"""
    @staticmethod
    def should_start_preprocessing():
        """是否应该开始预处理"""
        return (config.ENABLE_PREPROCESS and 
                state.frame_count >= config.PREPROCESS_START_FRAME and 
                not state.preprocess_started)
    
    @staticmethod
    def find_max_rectangles(contours):
        """查找最大的两个四边形，根据条件选择标定矩形"""
        if len(state.max_rectangles) >= 1:
            return True
        
        # 收集所有四边形候选，添加自重叠过滤
        rectangle_candidates = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area < config.MIN_AREA:
                continue
            
            epsilon = config.EPSILON_FACTOR * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)
            
            if len(approx) == 4:
                # 检查是否与已有候选重叠（自重叠过滤）
                is_duplicate = False
                M_new = cv2.moments(approx)
                if M_new["m00"] != 0:
                    cx_new = int(M_new["m10"] / M_new["m00"])
                    cy_new = int(M_new["m01"] / M_new["m00"])
                    
                    for existing_contour, existing_area, existing_approx in rectangle_candidates:
                        M_existing = cv2.moments(existing_approx)
                        if M_existing["m00"] != 0:
                            cx_existing = int(M_existing["m10"] / M_existing["m00"])
                            cy_existing = int(M_existing["m01"] / M_existing["m00"])
                            
                            distance = MathUtils.calculate_distance((cx_new, cy_new), (cx_existing, cy_existing))
                            area_ratio = MathUtils.calculate_area_ratio(area, existing_area)
                            
                            if (distance < config.DUPLICATE_DISTANCE_THRESHOLD and 
                                area_ratio > config.DUPLICATE_AREA_RATIO):
                                is_duplicate = True
                                print(f"🔄 检测到重复四边形，跳过：中心距离={distance:.1f}, 面积比例={area_ratio:.3f}")
                                break
                
                if not is_duplicate:
                    rectangle_candidates.append((contour, area, approx))
        
        # 按面积从大到小排序
        rectangle_candidates = sorted(rectangle_candidates, key=lambda x: x[1], reverse=True)
        
        print(f"🔍 预处理自重叠过滤完成，有效四边形候选数量: {len(rectangle_candidates)}")
        
        if len(rectangle_candidates) == 0:
            return False
        elif len(rectangle_candidates) == 1:
            # 只有一个四边形，直接作为标定矩形
            state.max_rectangles = [rectangle_candidates[0]]
            print(f"找到唯一标定矩形，面积: {rectangle_candidates[0][1]}")
            return True
        else:
            # 有两个或更多四边形，分析前两个
            largest_rect = rectangle_candidates[0]  # 最大的四边形
            second_rect = rectangle_candidates[1]   # 第二大的四边形
            
            # 计算两个四边形的中心点
            M1 = cv2.moments(largest_rect[2])  # largest_rect[2] 是 approx
            M2 = cv2.moments(second_rect[2])
            
            if M1["m00"] != 0 and M2["m00"] != 0:
                cx1 = int(M1["m10"] / M1["m00"])
                cy1 = int(M1["m01"] / M1["m00"])
                cx2 = int(M2["m10"] / M2["m00"])
                cy2 = int(M2["m01"] / M2["m00"])
                
                # 计算中心距离
                center_distance = MathUtils.calculate_distance((cx1, cy1), (cx2, cy2))
                
                # 计算面积比例（小的除以大的）
                area_ratio = second_rect[1] / largest_rect[1]
                
                print(f"双四边形分析: 最大面积={largest_rect[1]:.0f}, 第二大面积={second_rect[1]:.0f}")
                print(f"中心距离: {center_distance:.1f} 像素, 面积比例: {area_ratio:.3f}")
                
                # 检查是否满足条件：中心重合且小的是大的90%以上
                if (center_distance <= config.DUAL_RECT_CENTER_TOLERANCE and 
                    area_ratio >= config.DUAL_RECT_SIZE_RATIO_THRESHOLD):
                    # 满足条件，选择较小的四边形作为标定矩形
                    state.max_rectangles = [second_rect]
                    print(f"✅ 满足双四边形条件，选择较小的四边形作为标定矩形，面积: {second_rect[1]}")
                else:
                    # 不满足条件，选择最大的四边形作为标定矩形
                    state.max_rectangles = [largest_rect]
                    print(f"❌ 不满足双四边形条件，选择最大四边形作为标定矩形，面积: {largest_rect[1]}")
            else:
                # 计算矩心失败，选择最大的四边形
                state.max_rectangles = [largest_rect]
                print(f"计算矩心失败，选择最大四边形作为标定矩形，面积: {largest_rect[1]}")
            
            return True
    
    @staticmethod
    def update_masks_and_roi():
        """更新掩码和ROI"""
        if len(state.max_rectangles) != 1:
            return False
        
        # 检查是否需要更新
        current_centers = []
        _, _, approx = state.max_rectangles[0]
        M = cv2.moments(approx)
        if M["m00"] != 0:
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])
            current_centers.append((cx, cy))
        
        need_update = (not state.cached_mask_valid or 
                      len(state.last_rectangles_centers) != len(current_centers))
        
        if not need_update and len(state.last_rectangles_centers) == len(current_centers):
            for i, (cx, cy) in enumerate(current_centers):
                if i < len(state.last_rectangles_centers):
                    last_cx, last_cy = state.last_rectangles_centers[i]
                    distance = MathUtils.calculate_distance((cx, cy), (last_cx, last_cy))
                    if distance > 10:  # rectangle_position_threshold
                        need_update = True
                        break
        
        if need_update:
            try:
                # 创建掩码
                state.cached_mask = np.zeros((config.CAMERA_HEIGHT, config.CAMERA_WIDTH), dtype=np.uint8)
                _, _, approx = state.max_rectangles[0]
                cv2.drawContours(state.cached_mask, [approx], 0, 255, -1)
                
                mask_pixels = np.sum(state.cached_mask > 0)
                if mask_pixels < 100:
                    state.cached_mask_valid = False
                    state.preprocess_stable_frames = 0
                    return False
                
                state.cached_mask_valid = True
                state.last_rectangles_centers = current_centers.copy()
                
                # 计算ROI
                if config.USE_ROI_OPTIMIZATION:
                    all_points = []
                    for point in approx:
                        all_points.append(point[0])
                    
                    if all_points:
                        all_points = np.array(all_points)
                        min_x = max(0, np.min(all_points[:, 0]) - config.ROI_EXPAND_PIXELS)
                        max_x = min(config.CAMERA_WIDTH, np.max(all_points[:, 0]) + config.ROI_EXPAND_PIXELS)
                        min_y = max(0, np.min(all_points[:, 1]) - config.ROI_EXPAND_PIXELS)
                        max_y = min(config.CAMERA_HEIGHT, np.max(all_points[:, 1]) + config.ROI_EXPAND_PIXELS)
                        
                        state.cached_roi_rect = (int(min_x), int(min_y), int(max_x - min_x), int(max_y - min_y))
                        state.roi_valid = True
                
                state.preprocess_stable_frames += 1

                # 检查是否刚完成标定（稳定帧数刚好达到阈值）
                if state.preprocess_stable_frames == config.PREPROCESS_STABLE_THRESHOLD:
                    longest_edge_pixels = DistanceCalculator.get_max_rect_longest_edge()
                    print(f"🎯 预处理标定完成！标定四边形最长边像素值: {longest_edge_pixels:.2f} 像素")
                    print(f"📐 当前标定参数: 实际距离={config.CALIBRATION_DISTANCE}cm, 像素值={config.CALIBRATION_PIXELS}")
                    if longest_edge_pixels > 0:
                        print(f"💡 建议更新CALIBRATION_PIXELS参数为: {longest_edge_pixels:.2f}")

                return True
                
            except Exception as e:
                print(f"掩码创建出错: {e}")
                return False
        else:
            state.preprocess_stable_frames += 1

            # 检查是否刚完成标定（稳定帧数刚好达到阈值）
            if state.preprocess_stable_frames == config.PREPROCESS_STABLE_THRESHOLD:
                longest_edge_pixels = DistanceCalculator.get_max_rect_longest_edge()
                print(f"🎯 预处理标定完成！标定四边形最长边像素值: {longest_edge_pixels:.2f} 像素")
                print(f"📐 当前标定参数: 实际距离={config.CALIBRATION_DISTANCE}cm, 像素值={config.CALIBRATION_PIXELS}")
                if longest_edge_pixels > 0:
                    print(f"💡 建议更新CALIBRATION_PIXELS参数为: {longest_edge_pixels:.2f}")

            return True
    
    @staticmethod
    def filter_contours_by_mask(contours):
        """通过掩码过滤轮廓，排除标定矩形"""
        if not state.cached_mask_valid or state.preprocess_stable_frames < config.PREPROCESS_STABLE_THRESHOLD:
            return contours
        
        filtered_contours = []
        for contour in contours:
            M = cv2.moments(contour)
            if M["m00"] != 0:
                cx, cy = int(M["m10"] / M["m00"]), int(M["m01"] / M["m00"])
                
                # 检查是否是标定矩形
                is_calibration_rect = False
                if len(state.max_rectangles) >= 1:
                    _, _, max_approx = state.max_rectangles[0]
                    M_max = cv2.moments(max_approx)
                    if M_max["m00"] != 0:
                        cx_max = int(M_max["m10"] / M_max["m00"])
                        cy_max = int(M_max["m01"] / M_max["m00"])
                        
                        # 如果轮廓中心与标定矩形中心接近，且面积相近，认为是标定矩形
                        distance = MathUtils.calculate_distance((cx, cy), (cx_max, cy_max))
                        area = cv2.contourArea(contour)
                        max_area = cv2.contourArea(max_approx)
                        area_ratio = MathUtils.calculate_area_ratio(area, max_area)
                        
                        if (distance < config.DUPLICATE_DISTANCE_THRESHOLD and 
                            area_ratio > config.DUPLICATE_AREA_RATIO):
                            is_calibration_rect = True
                
                # 只有非标定矩形且在掩码内的轮廓才保留
                if (not is_calibration_rect and
                    0 <= cy < state.cached_mask.shape[0] and 
                    0 <= cx < state.cached_mask.shape[1] and 
                    state.cached_mask[cy, cx] > 0):
                    filtered_contours.append(contour)
        
        return filtered_contours

class ShapeProcessingPipeline:
    """形状处理流水线"""
    @staticmethod
    def detect_all_shapes(contours):
        """检测所有形状，排除标定矩形"""
        shape_candidates = {'Triangle': [], 'Quad': [], 'Circle': [], 'Polygon': []}
        detected_shapes = []
        
        for contour in contours:
            shape_info = ShapeDetector.classify_shape(contour)
            if shape_info and not ShapeDetector.is_duplicate_shape(shape_info, detected_shapes):
                # 检查是否是标定矩形
                is_calibration_rect = False
                if (shape_info['type'] == 'Quad' and len(state.max_rectangles) >= 1):
                    _, _, max_approx = state.max_rectangles[0]
                    M_max = cv2.moments(max_approx)
                    if M_max["m00"] != 0:
                        cx_max = int(M_max["m10"] / M_max["m00"])
                        cy_max = int(M_max["m01"] / M_max["m00"])
                        max_area = cv2.contourArea(max_approx)
                        
                        # 如果四边形与标定矩形位置和面积接近，认为是标定矩形
                        distance = MathUtils.calculate_distance(
                            (shape_info['cx'], shape_info['cy']), (cx_max, cy_max)
                        )
                        area_ratio = MathUtils.calculate_area_ratio(shape_info['area'], max_area)
                        
                        if (distance < config.DUPLICATE_DISTANCE_THRESHOLD and 
                            area_ratio > config.DUPLICATE_AREA_RATIO):
                            is_calibration_rect = True
                
                # 只有非标定矩形的形状才加入候选列表
                if not is_calibration_rect:
                    shape_type = shape_info['type']
                    if shape_type in shape_candidates:
                        shape_candidates[shape_type].append(shape_info)
                        detected_shapes.append((shape_info['cx'], shape_info['cy'], 
                                              shape_info['area'], shape_info['vertices_count']))
        
        return shape_candidates, detected_shapes
    
    @staticmethod
    def detect_polygons_in_quad_region(quad_approx, img_cv, edges, min_area, detected_shapes, 
                                     duplicate_distance_threshold, duplicate_area_ratio, img_result, 
                                     frame_count, shape_tracking_data, vertex_history, vertex_history_size, 
                                     position_tolerance, last_frame_shapes):
        """在四边形区域内检测多边形，使用更精细的epsilon_factor"""
        # 只在预处理完成后才进行多边形检测
        if not (config.ENABLE_PREPROCESS and state.preprocess_started and 
                len(state.max_rectangles) == 1 and 
                state.preprocess_stable_frames >= config.PREPROCESS_STABLE_THRESHOLD):
            return
        
        # ROI模式下检查四边形是否在ROI区域内
        if (config.USE_ROI_OPTIMIZATION and state.roi_valid and state.cached_roi_rect is not None and
            edges.shape != img_cv.shape[:2]):
            roi_x, roi_y, roi_w, roi_h = state.cached_roi_rect
            
            # 检查四边形的所有顶点是否都在ROI区域内
            all_vertices_in_roi = True
            for point in quad_approx:
                px, py = point[0]
                if not (roi_x <= px < roi_x + roi_w and roi_y <= py < roi_y + roi_h):
                    all_vertices_in_roi = False
                    break
            
            if not all_vertices_in_roi:
                return
        
        # 创建四边形区域的掩码
        if edges.shape != img_cv.shape[:2]:
            # ROI模式：调整quad_approx坐标
            if state.roi_valid and state.cached_roi_rect is not None:
                roi_x, roi_y, roi_w, roi_h = state.cached_roi_rect
                adjusted_quad_approx = quad_approx.copy()
                adjusted_quad_approx[:, 0, 0] -= roi_x
                adjusted_quad_approx[:, 0, 1] -= roi_y
                
                mask = np.zeros(edges.shape, dtype=np.uint8)
                cv2.fillPoly(mask, [adjusted_quad_approx], 255)
                
                # 扩展四边形区域以避免边缘效应
                if config.QUAD_MASK_EXPAND_PIXELS > 0:
                    kernel = np.ones((config.QUAD_MASK_EXPAND_PIXELS*2+1, config.QUAD_MASK_EXPAND_PIXELS*2+1), np.uint8)
                    mask = cv2.dilate(mask, kernel, iterations=1)
            else:
                return
        else:
            # 全图模式：直接使用原坐标
            mask = np.zeros(img_cv.shape[:2], dtype=np.uint8)
            cv2.fillPoly(mask, [quad_approx], 255)
            
            # 扩展四边形区域以避免边缘效应
            if config.QUAD_MASK_EXPAND_PIXELS > 0:
                kernel = np.ones((config.QUAD_MASK_EXPAND_PIXELS*2+1, config.QUAD_MASK_EXPAND_PIXELS*2+1), np.uint8)
                mask = cv2.dilate(mask, kernel, iterations=1)
        
        # 在四边形区域内查找轮廓
        masked_edges = cv2.bitwise_and(edges, mask)
        contours, _ = cv2.findContours(masked_edges, cv2.RETR_TREE, cv2.CHAIN_APPROX_NONE)
        
        # 使用更精细的epsilon_factor
        fine_epsilon_factor = 0.01
        
        try:
            for contour in contours:
                area = cv2.contourArea(contour)
                if area < min_area:
                    continue
                
                # 使用精细的epsilon_factor进行轮廓近似
                epsilon = fine_epsilon_factor * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                num_vertices = len(approx)
                
                # 只检测多边形（5个或更多顶点）
                if num_vertices >= 5:
                    M = cv2.moments(contour)
                    if M["m00"] == 0:
                        continue
                    
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    
                    # 如果是ROI模式，需要将坐标转换回原图坐标系
                    if edges.shape != img_cv.shape[:2] and state.roi_valid and state.cached_roi_rect is not None:
                        roi_x, roi_y, roi_w, roi_h = state.cached_roi_rect
                        cx += roi_x
                        cy += roi_y
                        # 调整approx中的顶点坐标
                        adjusted_approx = approx.copy()
                        adjusted_approx[:, 0, 0] += roi_x
                        adjusted_approx[:, 0, 1] += roi_y
                        approx = adjusted_approx
                    
                    # 检查是否是重复的多边形
                    is_duplicate = ShapeDetector.is_duplicate_shape(
                        {'cx': cx, 'cy': cy, 'area': area, 'vertices_count': num_vertices}, 
                        detected_shapes
                    )
                    
                    if is_duplicate:
                        continue
                    
                    shape = f"Polygon({num_vertices})"
                    color = image.COLOR_PURPLE
                    
                    # 添加到已检测形状列表
                    detected_shapes.append((cx, cy, area, num_vertices))
                    
                    # 在图像上标记识别结果
                    img_result.draw_string(cx-20, cy, shape, color)
                    if config.SHOW_SHAPE_AREAS:
                        area_text = f"A:{int(area)}"
                        img_result.draw_string(cx-20, cy+15, area_text, color)
                    
                    # 提取顶点坐标列表
                    vertices = [tuple(pt[0]) for pt in approx]
                    
                    # 查找匹配的形状位置并更新跟踪数据
                    shape_position = ShapeTracker.find_matching_position(cx, cy, "Polygon")
                    shape_position = ShapeTracker.update_tracking({
                        'type': 'Polygon', 'cx': cx, 'cy': cy, 'area': area, 'vertices': vertices
                    })
                    
                    # 记录该多边形到当前帧多边形列表
                    if "Polygon" not in last_frame_shapes:
                        last_frame_shapes["Polygon"] = []
                    last_frame_shapes["Polygon"].append((cx, cy, area))
                    
                    # 更新框内图形统计数据
                    if (config.ENABLE_INNER_SHAPE_STATS and config.ENABLE_PREPROCESS and 
                        state.preprocess_started and len(state.max_rectangles) == 1 and 
                        state.preprocess_stable_frames >= config.PREPROCESS_STABLE_THRESHOLD):
                        StatsUpdater.update_shape_stats({
                            'type': 'Polygon', 'vertices': vertices
                        })
                    
                    # 画出轮廓和拐点，并显示边长（完整的边长绘制功能）
                    if vertices:
                        for i in range(len(vertices)):
                            pt1, pt2 = vertices[i], vertices[(i+1) % len(vertices)]
                            
                            # 绘制边
                            img_result.draw_line(pt1[0], pt1[1], pt2[0], pt2[1], color, 2)
                            img_result.draw_circle(pt1[0], pt1[1], 3, color, thickness=-1)
                            
                            # 计算并显示边长
                            if config.SHOW_EDGE_LENGTHS:
                                edge_length = int(MathUtils.calculate_distance(pt1, pt2))
                                edge_key = (shape_position, i)
                                
                                if edge_key not in state.edge_history:
                                    state.edge_history[edge_key] = []
                                
                                state.edge_history[edge_key].append(edge_length)
                                if len(state.edge_history[edge_key]) > config.EDGE_HISTORY_SIZE:
                                    state.edge_history[edge_key].pop(0)
                                
                                display_length = UtilityFunctions.get_value_from_history(
                                    state.edge_history[edge_key], config.USE_INSTANT_VALUES
                                )
                                mid_x, mid_y = (pt1[0] + pt2[0]) // 2, (pt1[1] + pt2[1]) // 2
                                img_result.draw_string(mid_x, mid_y, str(display_length), image.COLOR_RED)
        
        except Exception as e:
            print(f"四边形区域多边形检测出错: {e}")
    
    @staticmethod
    def apply_overlap_filters(shape_candidates):
        """应用基于优先级的重叠过滤"""
        print("🎯 开始应用优先级过滤体系...")
        print(f"过滤前形状数量: 三角形={len(shape_candidates['Triangle'])}, 四边形={len(shape_candidates['Quad'])}, 圆形={len(shape_candidates['Circle'])}, 多边形={len(shape_candidates['Polygon'])}")
        
        # 使用新的优先级过滤系统
        filtered_results = ShapeFilter.apply_priority_based_filtering(shape_candidates)
        
        print(f"优先级过滤后形状数量: 三角形={len(filtered_results['Triangle'])}, 四边形={len(filtered_results['Quad'])}, 圆形={len(filtered_results['Circle'])}, 多边形={len(filtered_results['Polygon'])}")
        
        return filtered_results
    
    @staticmethod
    def process_and_render_shapes(filtered_shapes, img_result, img_cv, edges):
        """处理和渲染形状"""
        shape_counts = {'Triangle': 0, 'Quad': 0, 'Circle': 0, 'Polygon': 0}
        
        # 重置统计
        StatsUpdater.reset_stats()
        
        for shape_type, shapes in filtered_shapes.items():
            for shape_info in shapes:
                # 更新跟踪
                shape_position = ShapeTracker.update_tracking(shape_info)
                
                # 注意：标定矩形已经不在filtered_shapes中，所以这里不需要检查is_max_rect
                
                # 更新统计
                if (state.preprocess_started and len(state.max_rectangles) == 1 and 
                    state.preprocess_stable_frames >= config.PREPROCESS_STABLE_THRESHOLD):
                    StatsUpdater.update_shape_stats(shape_info)
                
                # 渲染形状（标定矩形已被排除，所以is_max_rect永远为False）
                Renderer.draw_shape(img_result, shape_info, shape_position, False, False)
                shape_counts[shape_type] += 1
                
                # 对保留的四边形进行精细多边形检测（移除条件限制，传递正确的detected_shapes）
                if (shape_type == 'Quad' and config.ENABLE_PREPROCESS and 
                    state.preprocess_started and len(state.max_rectangles) == 1 and 
                    state.preprocess_stable_frames >= config.PREPROCESS_STABLE_THRESHOLD):
                    
                    # 创建累积的detected_shapes列表（包含所有已处理的形状）
                    accumulated_detected_shapes = []
                    for processed_type, processed_shapes in filtered_shapes.items():
                        for processed_shape in processed_shapes:
                            accumulated_detected_shapes.append((
                                processed_shape['cx'], processed_shape['cy'], 
                                processed_shape['area'], processed_shape['vertices_count']
                            ))
                    
                    # 对所有四边形进行多边形检测（移除is_max_rect限制）
                    ShapeProcessingPipeline.detect_polygons_in_quad_region(
                        shape_info['approx'], img_cv, edges, config.MIN_AREA, 
                        accumulated_detected_shapes, config.DUPLICATE_DISTANCE_THRESHOLD, config.DUPLICATE_AREA_RATIO,
                        img_result, state.frame_count, state.shape_tracking_data, 
                        state.vertex_history, config.VERTEX_HISTORY_SIZE, 
                        config.POSITION_TOLERANCE, state.last_frame_shapes
                    )
        
        return shape_counts
    
    @staticmethod
    def _is_max_rectangle(quad_info):
        """检查四边形是否是最大矩形 - 注意：由于标定矩形已被排除在检测之外，此函数现在不会被调用"""
        if not state.max_rectangles or len(state.max_rectangles) < 1:
            return False
        
        quad_cx, quad_cy, quad_area = quad_info['cx'], quad_info['cy'], quad_info['area']
        
        _, max_area, max_approx = state.max_rectangles[0]
        # 计算最大矩形的中心点
        M_max = cv2.moments(max_approx)
        if M_max["m00"] != 0:
            cx_max = int(M_max["m10"] / M_max["m00"])
            cy_max = int(M_max["m01"] / M_max["m00"])
            
            # 计算距离和面积比
            distance = MathUtils.calculate_distance((quad_cx, quad_cy), (cx_max, cy_max))
            area_ratio = MathUtils.calculate_area_ratio(quad_area, max_area)
            
            # 判断是否是同一个矩形
            if (distance < config.DUPLICATE_DISTANCE_THRESHOLD and 
                area_ratio > config.DUPLICATE_AREA_RATIO):
                return True
        
        return False

def create_display_image(img_cv, binary, edges):
    """创建显示图像"""
    if not config.SHOW_DEBUG:
        return image.cv2image(img_cv, bgr=True, copy=False)
    
    if config.DEBUG_VIEW == 0:
        return image.cv2image(img_cv, bgr=True, copy=False)
    elif config.DEBUG_VIEW == 1:
        # 处理ROI二值图像
        if (config.USE_ROI_OPTIMIZATION and state.roi_valid and 
            state.preprocess_started and len(state.max_rectangles) == 1 and 
            state.preprocess_stable_frames >= config.PREPROCESS_STABLE_THRESHOLD):
            full_binary = np.zeros((config.CAMERA_HEIGHT, config.CAMERA_WIDTH), dtype=np.uint8)
            x, y, w, h = state.cached_roi_rect
            if binary.shape[0] == h and binary.shape[1] == w:
                full_binary[y:y+h, x:x+w] = binary
            else:
                full_binary = binary if binary.shape == (config.CAMERA_HEIGHT, config.CAMERA_WIDTH) else full_binary
            binary_colored = np.stack([full_binary, full_binary, full_binary], axis=2)
        else:
            binary_colored = np.stack([binary, binary, binary], axis=2)
        return image.cv2image(binary_colored)
    elif config.DEBUG_VIEW == 2:
        # 处理ROI边缘图像
        if (config.USE_ROI_OPTIMIZATION and state.roi_valid and 
            state.preprocess_started and len(state.max_rectangles) == 1 and 
            state.preprocess_stable_frames >= config.PREPROCESS_STABLE_THRESHOLD):
            full_edges = np.zeros((config.CAMERA_HEIGHT, config.CAMERA_WIDTH), dtype=np.uint8)
            x, y, w, h = state.cached_roi_rect
            if edges.shape[0] == h and edges.shape[1] == w:
                full_edges[y:y+h, x:x+w] = edges
            else:
                full_edges = edges if edges.shape == (config.CAMERA_HEIGHT, config.CAMERA_WIDTH) else full_edges
            edges_colored = np.stack([full_edges, full_edges, full_edges], axis=2)
        else:
            edges_colored = np.stack([edges, edges, edges], axis=2)
        return image.cv2image(edges_colored)

def process_single_frame(img_cv):
    """处理单帧图像"""
    # 1. 图像预处理
    roi_rect = state.cached_roi_rect if state.roi_valid else None
    gray, binary, edges, roi_offset = ImageProcessor.preprocess_image(img_cv, roi_rect)
    
    # 2. 轮廓检测
    contours = ImageProcessor.find_contours_with_roi_adjustment(edges, roi_offset)
    contours = sorted(contours, key=cv2.contourArea, reverse=True)
    
    # 3. 黑长条检测和标定
    detected_strips = BlackStripDetector.detect_black_strips(contours)
    state.detected_strips = detected_strips

    # 更新标定黑长条
    if detected_strips and not state.calibration_strip:
        DistanceMeasurement.update_calibration_strip(detected_strips)
    
    # 4. 形状检测和过滤（标定矩形已被排除，不参与重叠过滤）
    shape_candidates, detected_shapes = ShapeProcessingPipeline.detect_all_shapes(contours)
    
    # 4.1. 检查是否有标定黑长条
    calibration_complete = state.calibration_strip is not None

    if calibration_complete:
        print("✅ 黑长条标定完成，启用智能过滤系统")
        filtered_shapes = ShapeProcessingPipeline.apply_overlap_filters(shape_candidates)
    else:
        print("⏳ 等待黑长条标定，智能过滤系统待机中...")
        filtered_shapes = shape_candidates
    
    # 5. 创建显示图像
    img_result = create_display_image(img_cv, binary, edges)
    
    # 6. 处理和渲染形状
    shape_counts = ShapeProcessingPipeline.process_and_render_shapes(filtered_shapes, img_result, img_cv, edges)
    
    # 7. 绘制信息覆盖层
    Renderer.draw_info_overlay(img_result)
    
    # 8. 绘制黑长条和距离测量
    if state.calibration_strip:
        # 绘制标定黑长条
        strip = state.calibration_strip
        cx, cy = strip['center']

        # 绘制黑长条轮廓
        cv2.drawContours(img_result.to_numpy_ref(), [strip['contour']], -1, (0, 255, 255), 2)
        img_result.draw_string(cx-30, cy-30, "标定长条", image.COLOR_YELLOW)

        # 距离测量
        if config.ENABLE_DISTANCE_MEASUREMENT and detected_strips:
            for strip_info in detected_strips:
                if DistanceMeasurement.is_calibration_strip(strip_info):
                    distance = DistanceMeasurement.calculate_distance_from_strip(strip_info)
                    if distance:
                        strip_cx, strip_cy = strip_info['center']
                        distance_text = f"Dist:{distance:.1f}cm"
                        img_result.draw_string(strip_cx-30, strip_cy-50, distance_text, image.COLOR_ORANGE)
                    break
    
    # 9. 清理过期跟踪数据
    cleaned_count = ShapeTracker.cleanup_expired_shapes()
    if cleaned_count > 0:
        print(f"清理了 {cleaned_count} 个过期形状位置")
    
    # 9. 重置帧状态
    state.last_frame_shapes = {}
    
    # 10. 更新统计信息
    StatisticsManager.update_shape_statistics(
        filtered_shapes['Triangle'],
        filtered_shapes['Quad'], 
        filtered_shapes['Circle'],
        filtered_shapes['Polygon']
    )
    
    # 11. 打印统计信息
    total_shapes = sum(shape_counts.values())
    if (state.preprocess_started and len(state.max_rectangles) == 1 and 
        state.preprocess_stable_frames >= config.PREPROCESS_STABLE_THRESHOLD):
        print(f"========== 帧 {state.frame_count} ==========")
        print(f"图形检测: 三角形 {shape_counts['Triangle']} | 四边形 {shape_counts['Quad']} | 圆形 {shape_counts['Circle']} | 多边形 {shape_counts['Polygon']} | 总计 {total_shapes}")
        
        # 显示框内图形统计信息
        if config.ENABLE_INNER_SHAPE_STATS:
            stats_parts = []
            if state.inner_shapes_stats['triangles']['count'] > 0:
                stats_parts.append(f"三角形 {state.inner_shapes_stats['triangles']['count']}个 (平均边长 {state.inner_shapes_stats['triangles']['avg_edge_length_physical']:.1f}cm)")
            if state.inner_shapes_stats['quadrilaterals']['count'] > 0:
                stats_parts.append(f"四边形 {state.inner_shapes_stats['quadrilaterals']['count']}个 (平均边长 {state.inner_shapes_stats['quadrilaterals']['avg_edge_length_physical']:.1f}cm)")
            if state.inner_shapes_stats['circles']['count'] > 0:
                stats_parts.append(f"圆形 {state.inner_shapes_stats['circles']['count']}个 (平均半径 {state.inner_shapes_stats['circles']['avg_radius_physical']:.1f}cm)")
            if state.inner_shapes_stats['polygons']['count'] > 0:
                stats_parts.append(f"多边形 {state.inner_shapes_stats['polygons']['count']}个 (平均边长 {state.inner_shapes_stats['polygons']['avg_edge_length_physical']:.1f}cm)")
            
            if stats_parts:
                print(f"框内统计: " + " | ".join(stats_parts))
        
        if config.ENABLE_DISTANCE_MEASUREMENT and state.current_distance > 0:
            print(f"距离测量: {state.current_distance:.1f}cm")
        
        # 打印详细统计信息
        StatisticsManager.print_detailed_statistics()
    else:
        print(f"========== 帧 {state.frame_count} ==========")
        print(f"预处理中 - 图形检测: 总计 {total_shapes} 个图形")
    
    return img_result

# ==================== 主函数区 ====================

def main():
    """主函数"""
    # 初始化硬件
    cam = camera.Camera(config.CAMERA_WIDTH, config.CAMERA_HEIGHT, image.Format.FMT_BGR888)
    disp = display.Display()
    
    print("形状检测系统启动")
    print(f"分辨率: {config.CAMERA_WIDTH}x{config.CAMERA_HEIGHT}")
    print(f"预处理: {'启用' if config.ENABLE_PREPROCESS else '禁用'}")
    print(f"ROI优化: {'启用' if config.USE_ROI_OPTIMIZATION else '禁用'}")
    print(f"距离测量: {'启用' if config.ENABLE_DISTANCE_MEASUREMENT else '禁用'}")
    
    try:
        while not app.need_exit():
            # 计时开始
            t_start = time.ticks_ms()
            
            # 读取图像
            img_maix = cam.read()
            img_cv = image.image2cv(img_maix, ensure_bgr=True, copy=False)
            
            # 处理单帧
            img_result = process_single_frame(img_cv)
            
            # 显示结果
            disp.show(img_result)
            
            # 更新帧计数
            state.frame_count += 1
            
            # 计时结束（可选）
            # t_end = time.ticks_ms()
            # print(f"帧处理时间: {t_end - t_start}ms")
            
    except KeyboardInterrupt:
        print("程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
    finally:
        print("形状检测系统关闭")

if __name__ == "__main__":
    main()
