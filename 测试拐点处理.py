import cv2
import numpy as np

# 从初始代码引入的工具函数
def calculate_distance(point1, point2):
    """计算两点之间的欧几里得距离"""
    return np.sqrt((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)

def calculate_edge_overlap_with_polygon(edge_start, edge_end, polygon_points, overlap_threshold=0.5):
    """
    计算边与多边形的重叠程度
    返回重叠比例 (0.0 - 1.0)
    """
    try:
        if edge_start == edge_end:
            print("警告：边的起点和终点相同")
            return 0.0
        
        # 计算边长，如果太短则返回0
        edge_length = calculate_distance(edge_start, edge_end)
        if edge_length < 1.0:  # 边长小于1像素
            print(f"警告：边长太短 ({edge_length:.2f} pixels)")
            return 0.0
        
        # 简化的重叠计算：检查边的中点和几个采样点是否在多边形内
        sample_points = []
        num_samples = 5
        for i in range(num_samples):
            t = i / (num_samples - 1)
            sample_x = edge_start[0] + t * (edge_end[0] - edge_start[0])
            sample_y = edge_start[1] + t * (edge_end[1] - edge_start[1])
            sample_points.append((sample_x, sample_y))
        
        inside_count = 0
        for point in sample_points:
            if cv2.pointPolygonTest(polygon_points, point, False) >= 0:
                inside_count += 1
        
        overlap_ratio = inside_count / num_samples
        return overlap_ratio
        
    except Exception as e:
        print(f"计算边重叠度时出错: {e}")
        return 0.0

def process_contour_corners_test(contour_corners_list):
    """
    测试拐点处理函数
    """
    if not contour_corners_list:
        return {}
    
    overlap_threshold = 0.5  # 重叠阈值
    jd_dict = {}  # 用字典存储所有轮廓的拐点数组（键为"JD1"、"JD2"...）
    
    for contour_idx, corners in enumerate(contour_corners_list):
        contour_id = contour_idx + 1
        n = len(corners)
        if n == 0:
            continue
        
        # 定义当前轮廓的拐点数组名称（JD1、JD2...）
        jd_name = f"JD{contour_id}"
        
        # 提取顶点坐标
        vertices = [(corner[0], corner[1]) for corner in corners]
        
        # 使用初始代码的拐点分析方法
        corner_marks = []
        
        print(f"\n{jd_name}（轮廓{contour_id}的拐点分析，{n}个顶点）：")
        
        # 第一步：根据边与多边形重叠度进行初始标记
        initial_marks = []
        for i in range(len(vertices)):
            prev_vertex = vertices[(i - 1) % len(vertices)]
            next_vertex = vertices[(i + 1) % len(vertices)]
            
            # 计算当前拐点对应的边（连接前一个和下一个顶点）
            edge_start = prev_vertex
            edge_end = next_vertex
            
            # 将顶点列表转换为多边形轮廓格式
            polygon_points = np.array(vertices, dtype=np.int32)
            
            # 计算该边与多边形的重叠程度
            overlap_ratio = calculate_edge_overlap_with_polygon(
                edge_start, edge_end, polygon_points, overlap_threshold
            )
            
            # 根据重叠程度进行初始标记
            if overlap_ratio >= overlap_threshold:
                initial_marks.append(1)
                print(f"  拐点 {i} 初始标记为 1 (重叠度: {overlap_ratio:.3f} >= {overlap_threshold})")
            else:
                initial_marks.append(0)
                print(f"  拐点 {i} 初始标记为 0 (重叠度: {overlap_ratio:.3f} < {overlap_threshold})")
        
        # 第二步：识别孤立拐点并进行最终标记
        corner_marks = []
        isolated_count = 0
        
        print(f"  识别孤立拐点:")
        for i in range(len(vertices)):
            current_mark = initial_marks[i]
            prev_mark = initial_marks[(i - 1) % len(vertices)]
            next_mark = initial_marks[(i + 1) % len(vertices)]
            
            # 检查是否为孤立拐点：当前为有效拐点(1)，但前后都是无效拐点(0)
            if current_mark == 1 and prev_mark == 0 and next_mark == 0:
                corner_marks.append(2)  # 标记为孤立拐点
                isolated_count += 1
                print(f"    拐点 {i} 标记为 2 (孤立拐点: 前{prev_mark}-当前{current_mark}-后{next_mark})")
            else:
                corner_marks.append(current_mark)  # 保持原始标记
                if current_mark == 1:
                    print(f"    拐点 {i} 保持标记为 1 (非孤立: 前{prev_mark}-当前{current_mark}-后{next_mark})")
                else:
                    print(f"    拐点 {i} 保持标记为 0 (无效拐点)")
        
        print(f"  识别结果: 发现 {isolated_count} 个孤立拐点")
        
        # 初始化3*n数组（X坐标、Y坐标、拐点标志）
        jd_array = np.zeros((3, n), dtype=np.float32)
        
        for i, (vertex, mark) in enumerate(zip(vertices, corner_marks)):
            x, y = vertex
            # 填充数组
            jd_array[0, i] = x  # X坐标行
            jd_array[1, i] = y  # Y坐标行
            jd_array[2, i] = mark  # 标志行（0=无效，1=有效，2=孤立）
        
        # 存储到字典（键为JD1、JD2等）
        jd_dict[jd_name] = jd_array
        
        # 输出当前轮廓的拐点数组
        print("X坐标行：", jd_array[0])
        print("Y坐标行：", jd_array[1])
        print("标志行（0=无效，1=有效，2=孤立）：", jd_array[2])
        print("完整数组：\n", jd_array)
    
    return jd_dict

# 测试数据
if __name__ == "__main__":
    # 创建测试数据：一个四边形的角点
    test_corners = [
        [(100, 100), (200, 100), (200, 200), (100, 200)]  # 一个矩形
    ]
    
    # 转换为期望的格式 (x, y, contour_id, global_id, local_id)
    contour_corners_list = []
    for contour_idx, corners in enumerate(test_corners):
        contour_corners = []
        for local_id, (x, y) in enumerate(corners):
            contour_corners.append((x, y, contour_idx + 1, local_id + 1, local_id + 1))
        contour_corners_list.append(contour_corners)
    
    print("测试拐点处理功能...")
    print("输入数据：", contour_corners_list)
    
    # 测试拐点处理
    result = process_contour_corners_test(contour_corners_list)
    
    print("\n测试完成！")
    print("结果：", result)
