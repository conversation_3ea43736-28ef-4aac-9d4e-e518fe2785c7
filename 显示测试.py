from maix import image, display, app, time, camera
import cv2
import numpy as np

print("=== MaixPy 显示测试程序 ===")

# 测试1：显示器初始化
print("\n1. 测试显示器初始化...")
try:
    disp = display.Display()
    print("✓ 显示器初始化成功")
except Exception as e:
    print(f"✗ 显示器初始化失败: {e}")
    exit(1)

# 测试2：摄像头初始化
print("\n2. 测试摄像头初始化...")
try:
    cam = camera.Camera(640, 480, image.Format.FMT_GRAYSCALE)
    print("✓ 摄像头初始化成功")
except Exception as e:
    print(f"✗ 摄像头初始化失败: {e}")
    exit(1)

# 测试3：摄像头读取
print("\n3. 测试摄像头读取...")
try:
    img = cam.read()
    if img is None:
        print("✗ 摄像头读取失败，返回None")
        exit(1)
    else:
        print(f"✓ 摄像头读取成功，图像类型: {type(img)}")
except Exception as e:
    print(f"✗ 摄像头读取异常: {e}")
    exit(1)

# 测试4：图像转换
print("\n4. 测试图像转换...")
try:
    cv_img = image.image2cv(img, ensure_bgr=False, copy=False)
    print(f"✓ 图像转换成功，OpenCV图像形状: {cv_img.shape}")
except Exception as e:
    print(f"✗ 图像转换失败: {e}")
    exit(1)

# 测试5：创建测试图像
print("\n5. 创建测试图像...")
try:
    # 创建一个简单的测试图像
    test_img = np.zeros((200, 200, 3), dtype=np.uint8)
    # 绘制一个红色矩形
    cv2.rectangle(test_img, (50, 50), (150, 150), (0, 0, 255), 3)
    # 绘制一些文字
    cv2.putText(test_img, "TEST", (70, 100), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    print(f"✓ 测试图像创建成功，形状: {test_img.shape}")
except Exception as e:
    print(f"✗ 测试图像创建失败: {e}")
    exit(1)

# 测试6：图像显示转换
print("\n6. 测试图像显示转换...")
try:
    maix_img = image.cv2image(test_img, bgr=True, copy=False)
    print(f"✓ 图像显示转换成功，MaixPy图像类型: {type(maix_img)}")
except Exception as e:
    print(f"✗ 图像显示转换失败: {e}")
    exit(1)

# 测试7：显示测试图像
print("\n7. 测试显示...")
try:
    disp.show(maix_img)
    print("✓ 测试图像显示命令执行成功")
    print("请检查屏幕是否显示红色矩形和'TEST'文字")
except Exception as e:
    print(f"✗ 图像显示失败: {e}")

# 测试8：实际摄像头图像显示
print("\n8. 测试实际摄像头图像显示...")
frame_count = 0
max_frames = 10

while not app.need_exit() and frame_count < max_frames:
    try:
        # 读取摄像头图像
        img = cam.read()
        if img is None:
            print(f"帧 {frame_count}: 摄像头读取失败")
            continue
        
        # 转换为OpenCV格式
        cv_img = image.image2cv(img, ensure_bgr=False, copy=False)
        
        # 转换为BGR用于显示
        if len(cv_img.shape) == 2:  # 灰度图
            bgr_img = cv2.cvtColor(cv_img, cv2.COLOR_GRAY2BGR)
        else:
            bgr_img = cv_img
        
        # 添加帧计数文字
        cv2.putText(bgr_img, f"Frame {frame_count}", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        
        # 转换为MaixPy格式并显示
        maix_img = image.cv2image(bgr_img, bgr=True, copy=False)
        disp.show(maix_img)
        
        print(f"✓ 帧 {frame_count}: 显示成功")
        frame_count += 1
        
        # 短暂延时
        time.sleep_ms(100)
        
    except Exception as e:
        print(f"✗ 帧 {frame_count} 处理失败: {e}")
        break

print(f"\n=== 测试完成，共处理 {frame_count} 帧 ===")

if frame_count == 0:
    print("建议检查：")
    print("1. 摄像头是否正确连接")
    print("2. 显示器是否正确连接")
    print("3. MaixPy环境是否正确安装")
    print("4. 设备权限是否正确")
else:
    print("如果看到图像显示，说明系统正常工作")
    print("如果没有看到图像，可能是显示器连接问题")
