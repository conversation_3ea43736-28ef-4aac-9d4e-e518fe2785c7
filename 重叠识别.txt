from maix import image, display, app, time, camera
import cv2
import numpy as np

# 初始化显示器，用于显示处理后的图像
disp = display.Display()
# 初始化摄像头，设置分辨率为640x480，采集灰度图像
cam = camera.Camera(640, 480, image.Format.FMT_GRAYSCALE)

# 定义ROI（感兴趣区域）的参数，用于截取图像中需要处理的部分
roi_x = 228  # ROI区域左上角x坐标
roi_y = 84  # ROI区域左上角y坐标
roi_w = 200  # ROI区域宽度
roi_h = 310  # ROI区域高度

def ensure_multiple_of_two(img):
    """确保图像的宽度和高度都是2的倍数，避免后续处理可能出现的尺寸问题"""
    # 获取图像的高度和宽度
    h, w = img.shape[:2]
    # 计算调整后的宽度（若原宽度不是2的倍数，则减1）
    new_w = w if w % 2 == 0 else w - 1
    # 计算调整后的高度（若原高度不是2的倍数，则减1）
    new_h = h if h % 2 == 0 else h - 1
    # 若尺寸有调整，则 resize 图像到新尺寸
    if new_w != w or new_h != h:
        return cv2.resize(img, (new_w, new_h))
    return img

# 新增：处理轮廓角点数组的函数########################################################
def process_contour_corners(contour_corners_list, binary_img):
    """
    为每个轮廓创建独立的3*n角点数组（JD1、JD2...），并输出数组内容
    """
    if not contour_corners_list or binary_img is None:
        return
    
    region_size = 5  # 角点周围区域大小
    jd_dict = {}  # 用字典存储所有轮廓的角点数组（键为"JD1"、"JD2"...）
    
    for contour_idx, corners in enumerate(contour_corners_list):
        contour_id = contour_idx + 1
        n = len(corners)
        if n == 0:
            continue
        
        # 定义当前轮廓的角点数组名称（JD1、JD2...）
        jd_name = f"JD{contour_id}"
        
        # 初始化3*n数组（X坐标、Y坐标、角点标志）
        jd_array = np.zeros((3, n), dtype=np.float32)
        
        for i, corner in enumerate(corners):
            x, y, _, global_id, local_id = corner
            
            # 计算角点类型（1=凸，2=凹）
            h, w = binary_img.shape[:2]
            start_x = max(0, x - region_size)
            end_x = min(w, x + region_size + 1)
            start_y = max(0, y - region_size)
            end_y = min(h, y + region_size + 1)
            corner_region = binary_img[start_y:end_y, start_x:end_x]
            total_pixels = corner_region.size
            black_pixels = np.sum(corner_region == 0) if total_pixels > 0 else 0
            black_ratio = black_pixels / total_pixels if total_pixels > 0 else 0
            corner_type = 1 if black_ratio > 0.5 else 2
            
            # 填充数组
            jd_array[0, i] = x  # X坐标行
            jd_array[1, i] = y  # Y坐标行
            jd_array[2, i] = corner_type  # 标志行
        
        # 存储到字典（键为JD1、JD2等）
        jd_dict[jd_name] = jd_array
        
        # 输出当前轮廓的角点数组
        print(f"\n{jd_name}（轮廓{contour_id}的角点数组，3*{n}）：")
        print("X坐标行：", jd_array[0])
        print("Y坐标行：", jd_array[1])
        print("标志行（1=凸，2=凹）：", jd_array[2])
        print("完整数组：\n", jd_array)
    
    # 可选：返回所有JD数组（便于后续调用）
    return jd_dict

#############角点数组转为边数组######################
def convert_jd_to_edge_array(jd_dict):
    """
    将角点数组JDn转换为边数组（5*n）
    转换规则：
    - 边数组为5行n列（n为角点数量）
    - 第1行：当前角点X坐标
    - 第2行：当前角点Y坐标
    - 第3行：下一角点X坐标（最后一个角点的下一个为第一个角点）
    - 第4行：下一角点Y坐标
    - 第5行：边标志位（当前角点和下一角点标志都为1时为1，否则为2）
    """
    if not jd_dict:
        print("没有可处理的角点数组")
        return {}
    
    edge_arrays = {}  # 存储所有边数组，键为"Edge1"、"Edge2"...
    
    for jd_name, jd_array in jd_dict.items():
        # 提取轮廓ID，生成边数组名称（如JD1对应Edge1）
        contour_id = int(jd_name.replace("JD", ""))
        edge_name = f"Edge{contour_id}"
        
        n = jd_array.shape[1]  # 角点数量（边数量与角点数量相同）
        if n < 2:
            print(f"{jd_name}角点数量不足，无法生成边数组")
            continue
        
        # 初始化5*n边数组
        edge_array = np.zeros((5, n), dtype=np.float32)
        
        # 填充边数组
        for i in range(n):
            # 当前角点索引
            current_idx = i
            # 下一角点索引（闭合轮廓，最后一个连接到第一个）
            next_idx = (i + 1) % n
            
            # 1-2行：当前角点坐标
            edge_array[0, i] = jd_array[0, current_idx]  # 当前X
            edge_array[1, i] = jd_array[1, current_idx]  # 当前Y
            
            # 3-4行：下一角点坐标
            edge_array[2, i] = jd_array[0, next_idx]     # 下一个X
            edge_array[3, i] = jd_array[1, next_idx]     # 下一个Y
            
            # 第5行：边标志位（当前和下一角点都为1时为1，否则为2）
            current_flag = jd_array[2, current_idx]
            next_flag = jd_array[2, next_idx]
            edge_flag = 1 if (current_flag == 1 and next_flag == 1) else 2
            edge_array[4, i] = edge_flag
        
        # 存储边数组
        edge_arrays[edge_name] = edge_array
        
        # 输出转换结果
        print(f"\n{edge_name}（轮廓{contour_id}的边数组，5*{n}）：")
        print("第1行（当前X）：", edge_array[0])
        print("第2行（当前Y）：", edge_array[1])
        print("第3行（下一个X）：", edge_array[2])
        print("第4行（下一个Y）：", edge_array[3])
        print("第5行（边标志）：", edge_array[4])
        print("完整边数组：\n", edge_array)
    
    return edge_arrays

############计算整边长度##################
def get_valid_edge_lengths(edge_arrays):
    """
    计算所有标志位为1的边的边长，返回一行的数组
    参数：edge_arrays - 存储Edge1、Edge2等边数组的字典
    返回：包含所有有效边（标志位1）长度的1维数组
    """
    if not edge_arrays:
        return np.array([])
    
    valid_lengths = []
    
    # 遍历所有边数组
    for edge_name, edge_array in edge_arrays.items():
        # 提取标志位为1的边的索引
        valid_indices = np.where(edge_array[4] == 1)[0]
        
        # 计算这些边的长度
        for i in valid_indices:
            # 当前点坐标
            x1, y1 = edge_array[0, i], edge_array[1, i]
            # 下一点坐标
            x2, y2 = edge_array[2, i], edge_array[3, i]
            # 计算欧氏距离
            length = np.sqrt((x2 - x1)**2 + (y2 - y1)** 2)
            valid_lengths.append(length)
    
    # 转换为1行的数组返回
    return np.array(valid_lengths).reshape(1, -1)

############拼接不完整边#################
def filter_and_concat_edges(edge_arrays):
    """
    保留所有标志位不为1的边，删除标志位为1的边，然后拼接所有轮廓的边数组
    参数：edge_arrays - 存储Edge1、Edge2等边数组的字典
    返回：拼接后的5*n数组（n为所有保留边的总数）
    """
    if not edge_arrays:
        return np.array([])
    
    filtered_edges = []
    
    # 遍历所有边数组，筛选标志位不为1的边
    for edge_array in edge_arrays.values():
        # 提取标志位不为1的列索引
        keep_indices = np.where(edge_array[4] != 1)[0]
        if len(keep_indices) > 0:
            # 保留这些列
            filtered = edge_array[:, keep_indices]
            filtered_edges.append(filtered)
    
    # 如果没有保留的边，返回空数组
    if not filtered_edges:
        return np.array([])
    
    # 按列拼接所有保留的边数组（5*n）
    concatenated = np.hstack(filtered_edges)
    return concatenated

###########寻找平行的不完整边#############
def detect_parallel_edges(concatenated_edges, angle_tolerance):
    """
    从拼接后的边数组中识别相互平行的边组合
    参数：
        concatenated_edges: 拼接后的5*n边数组（5行，n列）
        angle_tolerance: 角度容忍度（度），小于该值认为角度相同（平行）
    返回：
        8*m数组，m为平行边组合数，前4行为第一条边两点坐标，后4行为第二条边两点坐标
    """
    if concatenated_edges.size == 0:
        return np.array([])
    
    # 提取所有边的坐标（忽略标志位）
    num_edges = concatenated_edges.shape[1]
    edges = []  # 存储每条边的两点坐标 (x1,y1,x2,y2)
    
    for i in range(num_edges):
        x1, y1 = concatenated_edges[0, i], concatenated_edges[1, i]
        x2, y2 = concatenated_edges[2, i], concatenated_edges[3, i]
        edges.append((x1, y1, x2, y2))
    
    # 计算每条边的"无向角度"（将方向相反的角度视为相同）
    # 处理方式：将角度归一化到 [0, π) 范围（180度内）
    edge_angles = []
    for (x1, y1, x2, y2) in edges:
        dx = x2 - x1
        dy = y2 - y1
        
        # 计算原始角度（弧度，范围(-π, π]）
        if dx == 0 and dy == 0:
            angle = 0.0  # 无效边
        else:
            angle = np.arctan2(dy, dx)
        
        # 归一化到 [0, π)：将负角度转为正角度，超过π的减去π
        # 例如：-30°（-π/6）→ 150°（5π/6）；210°（7π/6）→ 30°（π/6）
        angle = angle % np.pi  # 取模运算实现归一化
        edge_angles.append(angle)
    
    # 寻找所有平行边组合（i < j 避免重复组合）
    parallel_pairs = []
    for i in range(num_edges):
        for j in range(i + 1, num_edges):
            # 计算角度差（已归一化到[0,π)，直接计算绝对值）
            angle_diff = np.abs(edge_angles[i] - edge_angles[j]) * (180 / np.pi)  # 转为度
            
            # 角度差小于容忍度，认为平行（包括方向相反的情况）
            if angle_diff <= angle_tolerance:
                # 第一条边的两点坐标（4行）
                edge_i = [
                    edges[i][0],  # x1
                    edges[i][1],  # y1
                    edges[i][2],  # x2
                    edges[i][3]   # y2
                ]
                # 第二条边的两点坐标（4行）
                edge_j = [
                    edges[j][0],  # x1
                    edges[j][1],  # y1
                    edges[j][2],  # x2
                    edges[j][3]   # y2
                ]
                parallel_pairs.append(edge_i + edge_j)
    
    # 转换为8*m数组（8行，m列）
    if not parallel_pairs:
        return np.array([])
    
    # 转置使每行对应要求的坐标行（8行，m列）
    result = np.array(parallel_pairs).T
    return result

###########判别并计算不平行边的距离################
def calculate_parallel_edge_distances(parallel_edges, binary_img, black_tolerance):
    """
    处理平行边数组，判断其组成的四边形内部是否全黑，计算符合条件的边对距离
    参数：
        parallel_edges: 8*m的平行边数组（前4行为第一条边，后4行为第二条边）
        binary_img: 二值化图像（用于判断四边形内部是否全黑）
        black_tolerance: 黑色像素比例阈值（默认0.95，即≥95%视为全黑）
    返回：
        1*k的数组，k为符合条件的平行边对数量，元素为对应的距离
    """
    if parallel_edges.size == 0:
        return np.array([])
    
    distances = []
    m = parallel_edges.shape[1]  # 平行边组合数
    
    # 遍历每组平行边
    for i in range(m):
        # 提取两条边的四个端点坐标
        # 第一条边：(x1,y1) -> (x2,y2)
        x1, y1 = parallel_edges[0, i], parallel_edges[1, i]
        x2, y2 = parallel_edges[2, i], parallel_edges[3, i]
        # 第二条边：(x3,y3) -> (x4,y4)
        x3, y3 = parallel_edges[4, i], parallel_edges[5, i]
        x4, y4 = parallel_edges[6, i], parallel_edges[7, i]
        
        # 构建四边形的四个顶点（按顺序排列）
        # 这里假设两条平行边为对边，连接端点形成四边形
        pts = np.array([
            [x1, y1], [x2, y2],
            [x4, y4], [x3, y3]
        ], dtype=np.int32)
        
        # 获取四边形的边界框（用于裁剪图像区域）
        min_x = np.min(pts[:, 0])
        max_x = np.max(pts[:, 0])
        min_y = np.min(pts[:, 1])
        max_y = np.max(pts[:, 1])
        
        # 确保边界在图像范围内
        h, w = binary_img.shape[:2]
        min_x = max(0, min_x)
        max_x = min(w - 1, max_x)
        min_y = max(0, min_y)
        max_y = min(h - 1, max_y)
        
        # 如果边界框无效（面积为0），跳过
        if min_x >= max_x or min_y >= max_y:
            continue
        
        # 创建掩膜，标记四边形内部区域
        mask = np.zeros((h, w), dtype=np.uint8)
        cv2.fillPoly(mask, [pts], 255)  # 四边形内部填充白色
        
        # 裁剪出四边形区域
        roi_mask = mask[min_y:max_y+1, min_x:max_x+1]
        roi_img = binary_img[min_y:max_y+1, min_x:max_x+1]
        
        # 计算四边形内部的黑色像素比例
        total_pixels = np.sum(roi_mask == 255)  # 四边形内部总像素
        if total_pixels == 0:
            continue
        
        # 统计黑色像素（目标，0值）数量
        black_pixels = np.sum((roi_img == 0) & (roi_mask == 255))
        black_ratio = black_pixels / total_pixels
        
        # 如果黑色比例达到阈值，计算两条平行边的距离
        if black_ratio >= black_tolerance:
            # 计算两条平行线之间的距离（使用点到直线的距离公式）
            # 直线1：ax + by + c1 = 0
            a = y2 - y1
            b = x1 - x2
            c1 = x2*y1 - x1*y2
            
            # 取第二条边上的一点计算到直线1的距离
            # 距离公式：|a*x3 + b*y3 + c1| / sqrt(a² + b²)
            numerator = np.abs(a * x3 + b * y3 + c1)
            denominator = np.sqrt(a**2 + b**2)
            
            if denominator > 1e-6:  # 避免除以0
                distance = numerator / denominator
                distances.append(distance)
    
    # 转换为1行的数组返回
    return np.array(distances).reshape(1, -1)


#############################################################################
# 主循环：持续处理图像，直到需要退出程序
while not app.need_exit():
    # 从摄像头读取一帧图像
    img = cam.read()
    
    # 将MaixPy的image对象转换为OpenCV的numpy数组（灰度图，无需BGR转换）
    img = image.image2cv(img, ensure_bgr=False, copy=False)
    
    # 从原始图像中截取ROI区域（只处理感兴趣的部分）
    roi = img[roi_y:roi_y+roi_h, roi_x:roi_x+roi_w]
    
    # 对ROI区域进行二值化处理（阈值40，大于40为白(255)，小于等于40为黑(0)）
    ret, binary = cv2.threshold(roi, 60, 255, cv2.THRESH_BINARY)
    
    # 对二值化图像进行翻转（用于检测高亮区域，黑变白、白变黑）
    flipped_binary = cv2.bitwise_not(binary)
    
    # 在翻转后的图像上寻找轮廓（只检测外轮廓，轮廓点用简化方式存储）
    contours, _ = cv2.findContours(flipped_binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # 将二值化图像转换为BGR格式（以便绘制彩色标记，如红色角点、绿色轮廓）
    binary_bgr = cv2.cvtColor(binary, cv2.COLOR_GRAY2BGR)
    
    # 在BGR图像上绘制ROI区域的边框（红色，线宽2）
    cv2.rectangle(binary_bgr, (0, 0), (roi_w-1, roi_h-1), (0, 0, 255), 2)
    
    # 初始化存储所有角点的列表
    all_corners = []
    # 初始化存储每个轮廓角点的列表（每个元素是一个轮廓的角点数组）
    contour_corners_list = []  # 存储每个轮廓的角点数组
    # 轮廓全局编号计数器（从0开始，每处理一个轮廓加1）
    contour_id = 0
    # 角点全局编号计数器（从0开始，每检测一个角点加1）
    global_corner_id = 0
    
    # 遍历每个检测到的轮廓
    for contour in contours:
        # 轮廓编号递增（使ID从1开始）
        contour_id += 1
        
        # 计算轮廓的周长（闭合轮廓）
        perimeter = cv2.arcLength(contour, True)
        
        # 设置多边形逼近的精度（epsilon为周长的1%，值越小逼近越精确）
        epsilon = 0.01 * perimeter
        
        # 对轮廓进行多边形逼近，得到角点（approx为角点坐标的数组）
        approx = cv2.approxPolyDP(contour, epsilon, True)
        
        # 在BGR图像上绘制轮廓（绿色，线宽2）
        cv2.drawContours(binary_bgr, [contour], -1, (0, 255, 0), 2)
        
        # 计算轮廓的矩（用于求轮廓中心）
        M = cv2.moments(contour)
        # 若轮廓面积不为0（避免除0错误），计算中心坐标并绘制轮廓编号
        if M["m00"] != 0:
            cX = int(M["m10"] / M["m00"])  # 轮廓中心x坐标
            cY = int(M["m01"] / M["m00"])  # 轮廓中心y坐标
            # 在轮廓中心附近绘制轮廓编号（青色，字体大小0.5，线宽2）
            cv2.putText(binary_bgr, f"{contour_id}", (cX - 30, cY - 20),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 2)
        
        # 初始化当前轮廓的角点列表
        contour_corners = []
        # 角点局部编号计数器（每个轮廓内从0开始，递增后从1开始）
        local_corner_id = 0
        
        # 遍历逼近得到的每个角点
        for point in approx:
            # 全局角点编号递增
            global_corner_id += 1
            # 局部角点编号递增（当前轮廓内从1开始）
            local_corner_id += 1
            
            # 提取角点的x、y坐标（point是二维数组，取第一个元素）
            x, y = point[0]
            
            # 将角点信息添加到当前轮廓的角点列表（包含坐标、轮廓ID、全局/局部编号）
            contour_corners.append((x, y, contour_id, global_corner_id, local_corner_id))
            # 将角点信息添加到所有角点的列表
            all_corners.append((x, y, contour_id, global_corner_id, local_corner_id))
            
            # 在BGR图像上绘制角点（红色圆点，半径5，填充）
            cv2.circle(binary_bgr, (x, y), 5, (0, 0, 255), -1)
            # 在角点附近绘制局部编号（蓝色，字体大小0.5，线宽2）
            cv2.putText(binary_bgr, f"{local_corner_id}", (x+8, y-8), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)
        
        # 将当前轮廓的角点列表添加到总列表中
        contour_corners_list.append(contour_corners)  # 收集当前轮廓的角点数组
    
    # 新增：调用角点数组处理函数（仅操作contour_corners_list）
    jd_dict1=process_contour_corners(contour_corners_list,flipped_binary)
    edge_arrays1=convert_jd_to_edge_array(jd_dict1)
    SX1=get_valid_edge_lengths(edge_arrays1)
    print("完整边长度为:\n",SX1)
    edge_arrays2=filter_and_concat_edges(edge_arrays1)
    print("不完整边数组为:\n",edge_arrays2)
    paredge_arrays=detect_parallel_edges(edge_arrays2, 5.0)
    print("平行的不完整边数组为:\n",paredge_arrays)
    SX2=calculate_parallel_edge_distances(paredge_arrays, binary, 0.9)
    print("不完整边算出长度为:\n",SX2)
    # 若检测到角点，打印所有角点的信息
    if all_corners:
        print("\n检测到的角点:")
        for x, y, c_id, g_id, l_id in all_corners:
            # 将ROI内的坐标转换为原始图像中的坐标（加上ROI的偏移量）
            orig_x = x + roi_x
            orig_y = y + roi_y
            # 打印角点的全局/局部编号、所属轮廓及坐标信息
            print(f"角点 G{g_id}/L{l_id} (轮廓 {c_id}): ROI内坐标 ({x}, {y})，原始图像坐标 ({orig_x}, {orig_y})")
    
    # 确保图像尺寸是2的倍数（避免显示问题）
    binary_bgr = ensure_multiple_of_two(binary_bgr)
    
    # 将OpenCV的numpy数组转换为MaixPy的image对象（BGR格式），用于显示
    img_show = image.cv2image(binary_bgr, bgr=True, copy=False)
    # 在显示器上显示处理后的图像
    disp.show(img_show)